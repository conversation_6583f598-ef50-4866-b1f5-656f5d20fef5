{"openapi": "3.0.0", "info": {"title": "Easypanel API", "description": "This is an auto-generated documentation for the API.\n\nTo authenticate, you need to pass the `Authorization` header with the value `Bearer <token>`.\n\nYou can get your token by logging in with the `auth.login` endpoint.", "version": "2.19.0"}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer"}}}, "paths": {"/api/trpc/projects.getDockerContainers": {"get": {"operationId": "projects.getDockerContainers", "security": [{"bearerAuth": []}], "tags": ["Projects"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"service": {"type": "string"}}, "required": ["service"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/projects.getDockerServices": {"get": {"operationId": "projects.getDockerServices", "security": [{"bearerAuth": []}], "tags": ["Projects"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/projects.inspectProject": {"get": {"operationId": "projects.inspectProject", "security": [{"bearerAuth": []}], "tags": ["Projects"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/projects.listProjects": {"get": {"operationId": "projects.listProjects", "security": [{"bearerAuth": []}], "tags": ["Projects"], "responses": {"200": {"description": ""}}}}, "/api/trpc/projects.listProjectsAndServices": {"get": {"operationId": "projects.listProjectsAndServices", "security": [{"bearerAuth": []}], "tags": ["Projects"], "responses": {"200": {"description": ""}}}}, "/api/trpc/services.common.getServiceError": {"get": {"operationId": "services.common.getServiceError", "security": [{"bearerAuth": []}], "tags": ["Services / Common"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/services.common.getNotes": {"get": {"operationId": "services.common.getNotes", "security": [{"bearerAuth": []}], "tags": ["Services / Common"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/services.app.inspectService": {"get": {"operationId": "services.app.inspectService", "security": [{"bearerAuth": []}], "tags": ["Services / App"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/services.app.getExposedPorts": {"get": {"operationId": "services.app.getExposedPorts", "security": [{"bearerAuth": []}], "tags": ["Services / App"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/services.box.inspectService": {"get": {"operationId": "services.box.inspectService", "security": [{"bearerAuth": []}], "tags": ["Services / Box"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/services.box.listPresets": {"get": {"operationId": "services.box.listPresets", "security": [{"bearerAuth": []}], "tags": ["Services / Box"], "responses": {"200": {"description": ""}}}}, "/api/trpc/services.wordpress.inspectService": {"get": {"operationId": "services.wordpress.inspectService", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/services.wordpress.getUsers": {"get": {"operationId": "services.wordpress.getUsers", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/services.wordpress.getRoles": {"get": {"operationId": "services.wordpress.getRoles", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/services.wordpress.getMaintenanceMode": {"get": {"operationId": "services.wordpress.getMaintenanceMode", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/services.wordpress.getThemes": {"get": {"operationId": "services.wordpress.getThemes", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/services.wordpress.searchTheme": {"get": {"operationId": "services.wordpress.searchTheme", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "search": {"type": "string"}}, "required": ["projectName", "serviceName", "search"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/services.wordpress.getPlugins": {"get": {"operationId": "services.wordpress.getPlugins", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/services.wordpress.searchPlugin": {"get": {"operationId": "services.wordpress.searchPlugin", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "search": {"type": "string"}}, "required": ["projectName", "serviceName", "search"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/services.wordpress.getOptions": {"get": {"operationId": "services.wordpress.getOptions", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/services.wordpress.getDatabaseServices": {"get": {"operationId": "services.wordpress.getDatabaseServices", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/services.wordpress.getWpConfig": {"get": {"operationId": "services.wordpress.getWpConfig", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/services.wordpress.getProfile": {"get": {"operationId": "services.wordpress.getProfile", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "stage": {"type": "string", "enum": ["bootstrap", "main_query", "template"]}}, "required": ["projectName", "serviceName", "stage"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/services.mariadb.inspectService": {"get": {"operationId": "services.mariadb.inspectService", "security": [{"bearerAuth": []}], "tags": ["Services / Mariadb"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/services.mongo.inspectService": {"get": {"operationId": "services.mongo.inspectService", "security": [{"bearerAuth": []}], "tags": ["Services / Mongo"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/services.mysql.inspectService": {"get": {"operationId": "services.mysql.inspectService", "security": [{"bearerAuth": []}], "tags": ["Services / Mysql"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/services.postgres.inspectService": {"get": {"operationId": "services.postgres.inspectService", "security": [{"bearerAuth": []}], "tags": ["Services / Postgres"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/services.redis.inspectService": {"get": {"operationId": "services.redis.inspectService", "security": [{"bearerAuth": []}], "tags": ["Services / Redis"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/services.compose.inspectService": {"get": {"operationId": "services.compose.inspectService", "security": [{"bearerAuth": []}], "tags": ["Services / Compose"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/services.compose.getDockerServices": {"get": {"operationId": "services.compose.getDockerServices", "security": [{"bearerAuth": []}], "tags": ["Services / Compose"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/services.compose.getIssues": {"get": {"operationId": "services.compose.getIssues", "security": [{"bearerAuth": []}], "tags": ["Services / Compose"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/auth.getUser": {"get": {"operationId": "auth.getUser", "security": [{"bearerAuth": []}], "tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": ""}}}}, "/api/trpc/auth.getSession": {"get": {"operationId": "auth.getSession", "security": [{"bearerAuth": []}], "tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": ""}}}}, "/api/trpc/github.listRepos": {"get": {"operationId": "github.listRepos", "security": [{"bearerAuth": []}], "tags": ["<PERSON><PERSON><PERSON>"], "responses": {"200": {"description": ""}}}}, "/api/trpc/portalLicense.getLicensePayload": {"get": {"operationId": "portalLicense.getLicensePayload", "security": [{"bearerAuth": []}], "tags": ["Portal License"], "responses": {"200": {"description": ""}}}}, "/api/trpc/lemonLicense.getLicensePayload": {"get": {"operationId": "lemonLicense.getLicensePayload", "security": [{"bearerAuth": []}], "tags": ["Lemon License"], "responses": {"200": {"description": ""}}}}, "/api/trpc/update.getStatus": {"get": {"operationId": "update.getStatus", "security": [{"bearerAuth": []}], "tags": ["Update"], "responses": {"200": {"description": ""}}}}, "/api/trpc/setup.getStatus": {"get": {"operationId": "setup.getStatus", "security": [{"bearerAuth": []}], "tags": ["Setup"], "responses": {"200": {"description": ""}}}}, "/api/trpc/monitor.getSystemStats": {"get": {"operationId": "monitor.getSystemStats", "security": [{"bearerAuth": []}], "tags": ["Monitor"], "responses": {"200": {"description": ""}}}}, "/api/trpc/monitor.getStorageStats": {"get": {"operationId": "monitor.getStorageStats", "security": [{"bearerAuth": []}], "tags": ["Monitor"], "responses": {"200": {"description": ""}}}}, "/api/trpc/monitor.getAdvancedStats": {"get": {"operationId": "monitor.getAdvancedStats", "security": [{"bearerAuth": []}], "tags": ["Monitor"], "responses": {"200": {"description": ""}}}}, "/api/trpc/monitor.getServiceStats": {"get": {"operationId": "monitor.getServiceStats", "security": [{"bearerAuth": []}], "tags": ["Monitor"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/monitor.getDockerTaskStats": {"get": {"operationId": "monitor.getDockerTaskStats", "security": [{"bearerAuth": []}], "tags": ["Monitor"], "responses": {"200": {"description": ""}}}}, "/api/trpc/monitor.getMonitorTableData": {"get": {"operationId": "monitor.getMonitorTableData", "security": [{"bearerAuth": []}], "tags": ["Monitor"], "responses": {"200": {"description": ""}}}}, "/api/trpc/settings.getGithubToken": {"get": {"operationId": "settings.getGithubToken", "security": [{"bearerAuth": []}], "tags": ["Settings"], "responses": {"200": {"description": ""}}}}, "/api/trpc/settings.getDailyDockerCleanup": {"get": {"operationId": "settings.getDailyDockerCleanup", "security": [{"bearerAuth": []}], "tags": ["Settings"], "responses": {"200": {"description": ""}}}}, "/api/trpc/settings.getLetsEncryptEmail": {"get": {"operationId": "settings.getLetsEncryptEmail", "security": [{"bearerAuth": []}], "tags": ["Settings"], "responses": {"200": {"description": ""}}}}, "/api/trpc/settings.getPanelDomain": {"get": {"operationId": "settings.getPanelDomain", "security": [{"bearerAuth": []}], "tags": ["Settings"], "responses": {"200": {"description": ""}}}}, "/api/trpc/settings.getServerIp": {"get": {"operationId": "settings.getServerIp", "security": [{"bearerAuth": []}], "tags": ["Settings"], "responses": {"200": {"description": ""}}}}, "/api/trpc/settings.getDemoMode": {"get": {"operationId": "settings.getDemoMode", "security": [{"bearerAuth": []}], "tags": ["Settings"], "responses": {"200": {"description": ""}}}}, "/api/trpc/settings.getGoogleAnalyticsMeasurementId": {"get": {"operationId": "settings.getGoogleAnalyticsMeasurementId", "security": [{"bearerAuth": []}], "tags": ["Settings"], "responses": {"200": {"description": ""}}}}, "/api/trpc/users.listUsers": {"get": {"operationId": "users.listUsers", "security": [{"bearerAuth": []}], "tags": ["Users"], "responses": {"200": {"description": ""}}}}, "/api/trpc/git.getPublicKey": {"get": {"operationId": "git.getPublic<PERSON>ey", "security": [{"bearerAuth": []}], "tags": ["Git"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["serviceName", "projectName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/cluster.listNodes": {"get": {"operationId": "cluster.listNodes", "security": [{"bearerAuth": []}], "tags": ["Cluster"], "responses": {"200": {"description": ""}}}}, "/api/trpc/cluster.addWorkerCommand": {"get": {"operationId": "cluster.addWorkerCommand", "security": [{"bearerAuth": []}], "tags": ["Cluster"], "responses": {"200": {"description": ""}}}}, "/api/trpc/branding.getErrorPageSettings": {"get": {"operationId": "branding.getErrorPageSettings", "security": [{"bearerAuth": []}], "tags": ["Branding"], "responses": {"200": {"description": ""}}}}, "/api/trpc/branding.getBasicSettings": {"get": {"operationId": "branding.getBasicSettings", "security": [{"bearerAuth": []}], "tags": ["Branding"], "responses": {"200": {"description": ""}}}}, "/api/trpc/branding.getLogoSettings": {"get": {"operationId": "branding.getLogoSettings", "security": [{"bearerAuth": []}], "tags": ["Branding"], "responses": {"200": {"description": ""}}}}, "/api/trpc/branding.getCustomCodeSettings": {"get": {"operationId": "branding.getCustomCodeSettings", "security": [{"bearerAuth": []}], "tags": ["Branding"], "responses": {"200": {"description": ""}}}}, "/api/trpc/branding.getLinksSettings": {"get": {"operationId": "branding.getLinksSettings", "security": [{"bearerAuth": []}], "tags": ["Branding"], "responses": {"200": {"description": ""}}}}, "/api/trpc/branding.getInterfaceSettingsPublic": {"get": {"operationId": "branding.getInterfaceSettingsPublic", "security": [{"bearerAuth": []}], "tags": ["Branding"], "responses": {"200": {"description": ""}}}}, "/api/trpc/notifications.listNotificationChannels": {"get": {"operationId": "notifications.listNotificationChannels", "security": [{"bearerAuth": []}], "tags": ["Notifications"], "responses": {"200": {"description": ""}}}}, "/api/trpc/certificates.listCertificates": {"get": {"operationId": "certificates.listCertificates", "security": [{"bearerAuth": []}], "tags": ["Certificates"], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"domain": {"type": "object", "properties": {"main": {"type": "string"}}, "required": ["main"], "additionalProperties": false}}, "required": ["domain"], "additionalProperties": false}}}}}}}}, "/api/trpc/actions.listActions": {"get": {"operationId": "actions.listActions", "security": [{"bearerAuth": []}], "tags": ["Actions"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "type": {"type": "string"}, "limit": {"type": "number", "default": 8}}, "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/actions.getAction": {"get": {"operationId": "actions.getAction", "security": [{"bearerAuth": []}], "tags": ["Actions"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/traefik.getEnv": {"get": {"operationId": "traefik.getEnv", "security": [{"bearerAuth": []}], "tags": ["Traefik"], "responses": {"200": {"description": ""}}}}, "/api/trpc/traefik.getCustomConfig": {"get": {"operationId": "traefik.getCustomConfig", "security": [{"bearerAuth": []}], "tags": ["Traefik"], "responses": {"200": {"description": ""}}}}, "/api/trpc/traefik.getDashboard": {"get": {"operationId": "traefik.getDashboard", "security": [{"bearerAuth": []}], "tags": ["Traefik"], "responses": {"200": {"description": ""}}}}, "/api/trpc/cloudflareTunnel.getConfig": {"get": {"operationId": "cloudflareTunnel.getConfig", "security": [{"bearerAuth": []}], "tags": ["Cloudflare Tunnel"], "responses": {"200": {"description": ""}}}}, "/api/trpc/cloudflareTunnel.listAccounts": {"get": {"operationId": "cloudflareTunnel.listAccounts", "security": [{"bearerAuth": []}], "tags": ["Cloudflare Tunnel"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"apiToken": {"type": "string"}}, "required": ["apiToken"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/cloudflareTunnel.listTunnels": {"get": {"operationId": "cloudflareTunnel.listTunnels", "security": [{"bearerAuth": []}], "tags": ["Cloudflare Tunnel"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"apiToken": {"type": "string"}, "accountId": {"type": "string"}}, "required": ["apiToken", "accountId"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/cloudflareTunnel.listZones": {"get": {"operationId": "cloudflareTunnel.listZones", "security": [{"bearerAuth": []}], "tags": ["Cloudflare Tunnel"], "responses": {"200": {"description": ""}}}}, "/api/trpc/cloudflareTunnel.getTunnelRules": {"get": {"operationId": "cloudflareTunnel.getTunnelRules", "security": [{"bearerAuth": []}], "tags": ["Cloudflare Tunnel"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/domains.getPrimaryDomain": {"get": {"operationId": "domains.getPrimaryDomain", "security": [{"bearerAuth": []}], "tags": ["Domains"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/domains.listDomains": {"get": {"operationId": "domains.listDomains", "security": [{"bearerAuth": []}], "tags": ["Domains"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/storageProviders.common.list": {"get": {"operationId": "storageProviders.common.list", "security": [{"bearerAuth": []}], "tags": ["Storage Providers / Common"], "responses": {"200": {"description": ""}}}}, "/api/trpc/storageProviders.common.listOptions": {"get": {"operationId": "storageProviders.common.listOptions", "security": [{"bearerAuth": []}], "tags": ["Storage Providers / Common"], "responses": {"200": {"description": ""}}}}, "/api/trpc/backups.getBackupLog": {"get": {"operationId": "backups.getBackupLog", "security": [{"bearerAuth": []}], "tags": ["Backups"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/dockerBuilders.listDockerBuilders": {"get": {"operationId": "dockerBuilders.listDockerBuilders", "security": [{"bearerAuth": []}], "tags": ["Docker Builders"], "responses": {"200": {"description": ""}}}}, "/api/trpc/middlewares.listMiddlewares": {"get": {"operationId": "middlewares.listMiddlewares", "security": [{"bearerAuth": []}], "tags": ["Middlewares"], "responses": {"200": {"description": ""}}}}, "/api/trpc/volumeBackups.listVolumeMounts": {"get": {"operationId": "volumeBackups.listVolumeMounts", "security": [{"bearerAuth": []}], "tags": ["Volume Backups"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string"}, "serviceName": {"type": "string"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/volumeBackups.listVolumeBackups": {"get": {"operationId": "volumeBackups.listVolumeBackups", "security": [{"bearerAuth": []}], "tags": ["Volume Backups"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string"}, "serviceName": {"type": "string"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/mounts.listMounts": {"get": {"operationId": "mounts.listMounts", "security": [{"bearerAuth": []}], "tags": ["Mounts"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/ports.listPorts": {"get": {"operationId": "ports.listPorts", "security": [{"bearerAuth": []}], "tags": ["Ports"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/databaseBackups.listDatabaseBackups": {"get": {"operationId": "databaseBackups.listDatabaseBackups", "security": [{"bearerAuth": []}], "tags": ["Database Backups"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/databaseBackups.getServiceDatabases": {"get": {"operationId": "databaseBackups.getServiceDatabases", "security": [{"bearerAuth": []}], "tags": ["Database Backups"], "responses": {"200": {"description": ""}}, "parameters": [{"in": "query", "name": "input", "content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}]}}, "/api/trpc/projects.createProject": {"post": {"operationId": "projects.createProject", "security": [{"bearerAuth": []}], "tags": ["Projects"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"name": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["name"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/projects.destroyProject": {"post": {"operationId": "projects.destroyProject", "security": [{"bearerAuth": []}], "tags": ["Projects"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"name": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["name"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/projects.updateProjectEnv": {"post": {"operationId": "projects.updateProjectEnv", "security": [{"bearerAuth": []}], "tags": ["Projects"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "env": {"type": "string"}}, "required": ["projectName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/projects.updateAccess": {"post": {"operationId": "projects.updateAccess", "security": [{"bearerAuth": []}], "tags": ["Projects"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "userId": {"type": "string"}, "active": {"type": "boolean"}}, "required": ["projectName", "userId", "active"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.common.rename": {"post": {"operationId": "services.common.rename", "security": [{"bearerAuth": []}], "tags": ["Services / Common"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"oldProjectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "oldServiceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "newProjectName": {"$ref": "#/properties/json/properties/oldProjectName"}, "newServiceName": {"$ref": "#/properties/json/properties/oldServiceName"}}, "required": ["oldProjectName", "oldServiceName", "newProjectName", "newServiceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.common.setNotes": {"post": {"operationId": "services.common.setNotes", "security": [{"bearerAuth": []}], "tags": ["Services / Common"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "notes": {"type": "string", "default": ""}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.app.createService": {"post": {"operationId": "services.app.createService", "security": [{"bearerAuth": []}], "tags": ["Services / App"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "source": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "image"}, "image": {"type": "string"}, "username": {"type": "string"}, "password": {"type": "string"}}, "required": ["type", "image"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "github"}, "owner": {"type": "string", "minLength": 1}, "repo": {"type": "string", "minLength": 1}, "ref": {"type": "string", "minLength": 1}, "path": {"type": "string", "pattern": "^\\/"}, "autoDeploy": {"type": "boolean"}}, "required": ["type", "owner", "repo", "ref", "path", "autoDeploy"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "git"}, "repo": {"type": "string", "minLength": 1}, "ref": {"type": "string", "minLength": 1}, "path": {"type": "string", "pattern": "^\\/"}}, "required": ["type", "repo", "ref", "path"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "upload"}}, "required": ["type"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "dockerfile"}, "dockerfile": {"type": "string", "minLength": 1}}, "required": ["type", "dockerfile"], "additionalProperties": false}]}, "build": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "dockerfile"}, "file": {"type": "string"}}, "required": ["type"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "buildpacks"}, "buildpacksBuilder": {"type": "string", "default": "heroku/builder:24"}}, "required": ["type"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "heroku-buildpacks"}}, "required": ["type"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "paketo-buildpacks"}}, "required": ["type"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "nixpacks"}, "nixpacksVersion": {"type": "string", "default": "1.34.1"}, "installCommand": {"type": "string"}, "buildCommand": {"type": "string"}, "startCommand": {"type": "string"}, "nixPackages": {"type": "string"}, "aptPackages": {"type": "string"}}, "required": ["type"], "additionalProperties": false}]}, "env": {"type": "string", "default": ""}, "basicAuth": {"type": "array", "items": {"type": "object", "properties": {"username": {"type": "string"}, "password": {"type": "string"}}, "required": ["username", "password"], "additionalProperties": false}}, "deploy": {"type": "object", "properties": {"replicas": {"type": "number", "default": 1}, "command": {"type": ["string", "null"], "default": null}, "zeroDowntime": {"type": "boolean", "default": true}, "capAdd": {"type": "array", "items": {"type": "string"}}, "capDrop": {"type": "array", "items": {"type": "string"}}, "sysctls": {"type": "object", "additionalProperties": {"type": "string"}}, "groups": {"type": "array", "items": {"type": "string"}}, "tiniInit": {"type": "boolean"}}, "additionalProperties": false, "default": {}}, "domains": {"type": "array", "items": {"type": "object", "properties": {"host": {"type": "string", "pattern": "^[^\\s*]+$"}, "https": {"type": "boolean", "default": true}, "port": {"type": "number", "default": 80}, "path": {"type": "string", "pattern": "^\\/", "default": "/"}, "middlewares": {"type": "array", "items": {"type": "string"}}, "certificateResolver": {"type": "string"}, "wildcard": {"type": "boolean", "default": false}, "internalProtocol": {"type": "string", "enum": ["http", "https"], "default": "http"}, "service": {"type": "string"}}, "required": ["host"], "additionalProperties": false}, "default": []}, "mounts": {"type": "array", "items": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "bind"}, "hostPath": {"type": "string", "minLength": 1}, "mountPath": {"type": "string", "minLength": 1}}, "required": ["type", "hostPath", "mount<PERSON>ath"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "volume"}, "name": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "mountPath": {"type": "string", "minLength": 1}}, "required": ["type", "name", "mount<PERSON>ath"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "file"}, "content": {"type": "string"}, "mountPath": {"type": "string", "minLength": 1}}, "required": ["type", "content", "mount<PERSON>ath"], "additionalProperties": false}]}, "default": []}, "ports": {"type": "array", "items": {"type": "object", "properties": {"published": {"type": "number"}, "target": {"type": "number"}, "protocol": {"type": "string", "enum": ["tcp", "udp"], "default": "tcp"}}, "required": ["published", "target"], "additionalProperties": false}, "default": []}, "resources": {"type": "object", "properties": {"memoryReservation": {"type": "number"}, "memoryLimit": {"type": "number"}, "cpuReservation": {"type": "number"}, "cpuLimit": {"type": "number"}}, "required": ["memoryReservation", "memoryLimit", "cpuReservation", "cpuLimit"], "additionalProperties": false}, "maintenance": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "title": {"type": "string"}, "subtitle": {"type": "string"}, "customLogo": {"type": "string"}, "customCss": {"type": "string"}, "hideLogo": {"type": "boolean"}, "hideLinks": {"type": "boolean"}}, "required": ["enabled"], "additionalProperties": false}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.app.updateDeploy": {"post": {"operationId": "services.app.updateDeploy", "security": [{"bearerAuth": []}], "tags": ["Services / App"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "deploy": {"type": "object", "properties": {"replicas": {"type": "number", "default": 1}, "command": {"type": ["string", "null"], "default": null}, "zeroDowntime": {"type": "boolean", "default": true}, "capAdd": {"type": "array", "items": {"type": "string"}}, "capDrop": {"type": "array", "items": {"type": "string"}}, "sysctls": {"type": "object", "additionalProperties": {"type": "string"}}, "groups": {"type": "array", "items": {"type": "string"}}, "tiniInit": {"type": "boolean"}}, "additionalProperties": false, "default": {}}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.app.updateResources": {"post": {"operationId": "services.app.updateResources", "security": [{"bearerAuth": []}], "tags": ["Services / App"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "resources": {"type": "object", "properties": {"memoryReservation": {"type": "number"}, "memoryLimit": {"type": "number"}, "cpuReservation": {"type": "number"}, "cpuLimit": {"type": "number"}}, "required": ["memoryReservation", "memoryLimit", "cpuReservation", "cpuLimit"], "additionalProperties": false}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.app.updateEnv": {"post": {"operationId": "services.app.updateEnv", "security": [{"bearerAuth": []}], "tags": ["Services / App"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "env": {"type": "string", "default": ""}, "createDotEnv": {"type": "boolean"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.app.updatePorts": {"post": {"operationId": "services.app.updatePorts", "security": [{"bearerAuth": []}], "tags": ["Services / App"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "ports": {"type": "array", "items": {"type": "object", "properties": {"published": {"type": "number"}, "target": {"type": "number"}, "protocol": {"type": "string", "enum": ["tcp", "udp"], "default": "tcp"}}, "required": ["published", "target"], "additionalProperties": false}, "default": []}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.app.updateBasicAuth": {"post": {"operationId": "services.app.updateBasicAuth", "security": [{"bearerAuth": []}], "tags": ["Services / App"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "basicAuth": {"type": "array", "items": {"type": "object", "properties": {"username": {"type": "string"}, "password": {"type": "string"}}, "required": ["username", "password"], "additionalProperties": false}}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.app.updateProxy": {"post": {"operationId": "services.app.updateProxy", "security": [{"bearerAuth": []}], "tags": ["Services / App"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "proxy": {"type": "object", "properties": {"port": {"type": "number", "minimum": 0, "maximum": 65535}, "secure": {"type": "boolean"}}, "required": ["port", "secure"], "additionalProperties": false}}, "required": ["projectName", "serviceName", "proxy"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.app.updateRedirects": {"post": {"operationId": "services.app.updateRedirects", "security": [{"bearerAuth": []}], "tags": ["Services / App"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "redirects": {"type": "array", "items": {"type": "object", "properties": {"regex": {"type": "string"}, "replacement": {"type": "string"}, "permanent": {"type": "boolean"}, "enabled": {"type": "boolean"}}, "required": ["regex", "replacement", "permanent", "enabled"], "additionalProperties": false}}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.app.updateSourceGithub": {"post": {"operationId": "services.app.updateSourceGithub", "security": [{"bearerAuth": []}], "tags": ["Services / App"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "owner": {"type": "string", "minLength": 1}, "repo": {"type": "string", "minLength": 1}, "ref": {"type": "string", "minLength": 1}, "path": {"type": "string", "pattern": "^\\/"}}, "required": ["projectName", "serviceName", "owner", "repo", "ref", "path"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.app.updateSourceGit": {"post": {"operationId": "services.app.updateSourceGit", "security": [{"bearerAuth": []}], "tags": ["Services / App"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "repo": {"type": "string", "minLength": 1}, "ref": {"type": "string", "minLength": 1}, "path": {"type": "string", "pattern": "^\\/"}}, "required": ["projectName", "serviceName", "repo", "ref", "path"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.app.enableGithubDeploy": {"post": {"operationId": "services.app.enableGithubDeploy", "security": [{"bearerAuth": []}], "tags": ["Services / App"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.app.disableGithubDeploy": {"post": {"operationId": "services.app.disableGithubDeploy", "security": [{"bearerAuth": []}], "tags": ["Services / App"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.app.updateBuild": {"post": {"operationId": "services.app.updateBuild", "security": [{"bearerAuth": []}], "tags": ["Services / App"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "build": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "dockerfile"}, "file": {"type": "string"}}, "required": ["type"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "buildpacks"}, "buildpacksBuilder": {"type": "string", "default": "heroku/builder:24"}}, "required": ["type"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "heroku-buildpacks"}}, "required": ["type"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "paketo-buildpacks"}}, "required": ["type"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "nixpacks"}, "nixpacksVersion": {"type": "string", "default": "1.34.1"}, "installCommand": {"type": "string"}, "buildCommand": {"type": "string"}, "startCommand": {"type": "string"}, "nixPackages": {"type": "string"}, "aptPackages": {"type": "string"}}, "required": ["type"], "additionalProperties": false}]}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.app.refreshDeployToken": {"post": {"operationId": "services.app.refreshDeployToken", "security": [{"bearerAuth": []}], "tags": ["Services / App"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.app.updateSourceImage": {"post": {"operationId": "services.app.updateSourceImage", "security": [{"bearerAuth": []}], "tags": ["Services / App"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "image": {"type": "string", "minLength": 1}, "username": {"type": "string"}, "password": {"type": "string"}}, "required": ["projectName", "serviceName", "image"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.app.updateSourceDockerfile": {"post": {"operationId": "services.app.updateSourceDockerfile", "security": [{"bearerAuth": []}], "tags": ["Services / App"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "dockerfile": {"type": "string", "minLength": 1}}, "required": ["projectName", "serviceName", "dockerfile"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.app.updateMounts": {"post": {"operationId": "services.app.updateMounts", "security": [{"bearerAuth": []}], "tags": ["Services / App"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "mounts": {"type": "array", "items": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "bind"}, "hostPath": {"type": "string", "minLength": 1}, "mountPath": {"type": "string", "minLength": 1}}, "required": ["type", "hostPath", "mount<PERSON>ath"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "volume"}, "name": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "mountPath": {"type": "string", "minLength": 1}}, "required": ["type", "name", "mount<PERSON>ath"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "file"}, "content": {"type": "string"}, "mountPath": {"type": "string", "minLength": 1}}, "required": ["type", "content", "mount<PERSON>ath"], "additionalProperties": false}]}, "default": []}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.app.deployService": {"post": {"operationId": "services.app.deployService", "security": [{"bearerAuth": []}], "tags": ["Services / App"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "forceRebuild": {"type": "boolean", "default": false}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.app.destroyService": {"post": {"operationId": "services.app.destroyService", "security": [{"bearerAuth": []}], "tags": ["Services / App"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.app.startService": {"post": {"operationId": "services.app.startService", "security": [{"bearerAuth": []}], "tags": ["Services / App"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.app.stopService": {"post": {"operationId": "services.app.stopService", "security": [{"bearerAuth": []}], "tags": ["Services / App"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.app.restartService": {"post": {"operationId": "services.app.restartService", "security": [{"bearerAuth": []}], "tags": ["Services / App"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.app.updateMaintenance": {"post": {"operationId": "services.app.updateMaintenance", "security": [{"bearerAuth": []}], "tags": ["Services / App"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "maintenance": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "title": {"type": "string"}, "subtitle": {"type": "string"}, "customLogo": {"type": "string"}, "customCss": {"type": "string"}, "hideLogo": {"type": "boolean"}, "hideLinks": {"type": "boolean"}}, "required": ["enabled"], "additionalProperties": false}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.app.uploadCodeArchive": {"post": {"operationId": "services.app.uploadCodeArchive", "security": [{"bearerAuth": []}], "tags": ["Services / App"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "archivePath": {"type": "string"}}, "required": ["projectName", "serviceName", "archivePath"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.box.createService": {"post": {"operationId": "services.box.createService", "security": [{"bearerAuth": []}], "tags": ["Services / Box"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "codeInitialized": {"type": "boolean", "default": false}, "deployment": {"type": "object", "properties": {"script": {"type": "string", "default": ""}, "token": {"type": "string", "default": "93a611d63fb87b8c6eb9"}}, "additionalProperties": false, "default": {}}, "git": {"type": "object", "properties": {"name": {"type": "string"}, "email": {"type": "string"}, "url": {"type": "string"}, "branch": {"type": "string"}}, "additionalProperties": false}, "domains": {"type": "array", "items": {"type": "object", "properties": {"host": {"type": "string", "pattern": "^[^\\s*]+$"}, "https": {"type": "boolean", "default": true}, "port": {"type": "number", "default": 80}, "path": {"type": "string", "pattern": "^\\/", "default": "/"}, "middlewares": {"type": "array", "items": {"type": "string"}}, "certificateResolver": {"type": "string"}, "wildcard": {"type": "boolean", "default": false}, "internalProtocol": {"type": "string", "enum": ["http", "https"], "default": "http"}, "service": {"type": "string"}}, "required": ["host"], "additionalProperties": false}, "default": []}, "redirects": {"type": "array", "items": {"type": "object", "properties": {"regex": {"type": "string"}, "replacement": {"type": "string"}, "permanent": {"type": "boolean"}, "enabled": {"type": "boolean"}}, "required": ["regex", "replacement", "permanent", "enabled"], "additionalProperties": false}, "default": []}, "basicAuth": {"type": "array", "items": {"type": "object", "properties": {"username": {"type": "string"}, "password": {"type": "string"}}, "required": ["username", "password"], "additionalProperties": false}, "default": []}, "mounts": {"type": "array", "items": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "bind"}, "hostPath": {"type": "string", "minLength": 1}, "mountPath": {"type": "string", "minLength": 1}}, "required": ["type", "hostPath", "mount<PERSON>ath"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "volume"}, "name": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "mountPath": {"type": "string", "minLength": 1}}, "required": ["type", "name", "mount<PERSON>ath"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "file"}, "content": {"type": "string"}, "mountPath": {"type": "string", "minLength": 1}}, "required": ["type", "content", "mount<PERSON>ath"], "additionalProperties": false}]}, "default": []}, "processes": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "command": {"type": "string"}, "directory": {"type": "string"}, "enabled": {"type": "boolean"}}, "required": ["name", "command", "directory", "enabled"], "additionalProperties": false}, "default": []}, "scripts": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "content": {"type": "string"}, "webhookToken": {"type": "string"}, "schedule": {"type": "string"}, "enabled": {"type": "boolean"}}, "required": ["name", "content", "webhookToken", "enabled"], "additionalProperties": false}, "default": []}, "ports": {"type": "array", "items": {"type": "object", "properties": {"published": {"type": "number"}, "target": {"type": "number"}, "protocol": {"type": "string", "enum": ["tcp", "udp"], "default": "tcp"}}, "required": ["published", "target"], "additionalProperties": false}, "default": []}, "resources": {"type": "object", "properties": {"memoryReservation": {"type": "number"}, "memoryLimit": {"type": "number"}, "cpuReservation": {"type": "number"}, "cpuLimit": {"type": "number"}}, "required": ["memoryReservation", "memoryLimit", "cpuReservation", "cpuLimit"], "additionalProperties": false}, "modules": {"type": "object", "properties": {"ide": {"type": "boolean", "default": false}, "advanced": {"type": "boolean", "default": false}, "php": {"type": "boolean", "default": false}, "nodejs": {"type": "boolean", "default": false}, "nginx": {"type": "boolean", "default": false}, "python": {"type": "boolean", "default": false}, "ruby": {"type": "boolean", "default": false}, "deployments": {"type": "boolean", "default": false}, "git": {"type": "boolean", "default": false}, "domains": {"type": "boolean", "default": false}, "redirects": {"type": "boolean", "default": false}, "basicAuth": {"type": "boolean", "default": false}, "mounts": {"type": "boolean", "default": false}, "processes": {"type": "boolean", "default": false}, "scripts": {"type": "boolean", "default": false}, "ports": {"type": "boolean", "default": false}, "resources": {"type": "boolean", "default": false}, "env": {"type": "boolean", "default": false}}, "additionalProperties": false, "default": {}}, "ide": {"type": "object", "properties": {"defaultFolder": {"type": "string", "default": "/code"}, "token": {"type": "string", "default": "215218a231062ee8c1dc"}, "enabled": {"type": "boolean", "default": true}}, "additionalProperties": false}, "advanced": {"type": "object", "properties": {"buildScript": {"type": "string", "default": ""}, "startScript": {"type": "string", "default": ""}}, "additionalProperties": false}, "php": {"type": "object", "properties": {"version": {"type": "string", "default": "8.3"}, "maxUploadSize": {"type": "string", "default": "128M"}, "maxExecutionTime": {"type": "string", "default": "30"}, "opcache": {"type": "boolean", "default": true}, "phpIni": {"type": "string", "default": ""}, "enabled": {"type": "boolean", "default": true}, "ioncube": {"type": "boolean", "default": false}, "sqlsrv": {"type": "boolean", "default": false}}, "additionalProperties": false}, "nodejs": {"type": "object", "properties": {"version": {"type": "string", "default": "18"}, "yarn": {"type": "boolean", "default": false}, "pnpm": {"type": "boolean", "default": false}, "enabled": {"type": "boolean", "default": true}}, "additionalProperties": false}, "nginx": {"type": "object", "properties": {"rootDocument": {"type": "string", "default": "/code"}, "config": {"type": "string", "default": "server {\n\tlisten 80 default_server;\n\tlisten [::]:80 default_server;\n\n\troot {{ document_root }};\n\n\tindex index.php index.html;\n\n\tserver_name _;\n\n\tlocation / {\n\t\ttry_files $uri $uri/ =404;\n\t}\n\n\tlocation ~ .php$ {\n\t\tinclude snippets/fastcgi-php.conf;\n\t\tfastcgi_pass {{ fpm_socket }};\n\t}\n}"}, "enabled": {"type": "boolean", "default": true}}, "additionalProperties": false}, "python": {"type": "object", "properties": {"version": {"type": "string", "default": "3"}, "enabled": {"type": "boolean", "default": false}}, "additionalProperties": false}, "ruby": {"type": "object", "properties": {"version": {"type": "string", "default": "2.6"}, "enabled": {"type": "boolean", "default": false}}, "additionalProperties": false}, "env": {"type": "object", "properties": {"content": {"type": "string", "default": ""}}, "additionalProperties": false}, "presetKey": {"type": "string"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.box.initService": {"post": {"operationId": "services.box.initService", "security": [{"bearerAuth": []}], "tags": ["Services / Box"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "git": {"type": "object", "properties": {"name": {"type": "string"}, "email": {"type": "string"}, "url": {"type": "string"}, "branch": {"type": "string"}}, "additionalProperties": false}, "private": {"type": "boolean", "default": true}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.box.updateProcesses": {"post": {"operationId": "services.box.updateProcesses", "security": [{"bearerAuth": []}], "tags": ["Services / Box"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "processes": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "command": {"type": "string"}, "directory": {"type": "string"}, "enabled": {"type": "boolean"}}, "required": ["name", "command", "directory", "enabled"], "additionalProperties": false}, "default": []}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.box.runScript": {"post": {"operationId": "services.box.runScript", "security": [{"bearerAuth": []}], "tags": ["Services / Box"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "name": {"type": "string"}, "content": {"type": "string"}}, "required": ["projectName", "serviceName", "name", "content"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.box.updateScripts": {"post": {"operationId": "services.box.updateScripts", "security": [{"bearerAuth": []}], "tags": ["Services / Box"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "scripts": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "content": {"type": "string"}, "webhookToken": {"type": "string"}, "schedule": {"type": "string"}, "enabled": {"type": "boolean"}}, "required": ["name", "content", "webhookToken", "enabled"], "additionalProperties": false}, "default": []}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.box.destroyService": {"post": {"operationId": "services.box.destroyService", "security": [{"bearerAuth": []}], "tags": ["Services / Box"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.box.gitClone": {"post": {"operationId": "services.box.gitClone", "security": [{"bearerAuth": []}], "tags": ["Services / Box"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "url": {"type": "string"}, "branch": {"type": "string"}, "private": {"type": "boolean", "default": true}}, "required": ["projectName", "serviceName", "url", "branch"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.box.updateGitConfig": {"post": {"operationId": "services.box.updateGitConfig", "security": [{"bearerAuth": []}], "tags": ["Services / Box"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "name": {"type": "string"}, "email": {"type": "string"}}, "required": ["projectName", "serviceName", "name", "email"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.box.refreshDeployToken": {"post": {"operationId": "services.box.refreshDeployToken", "security": [{"bearerAuth": []}], "tags": ["Services / Box"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["serviceName", "projectName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.box.rebuildDockerImage": {"post": {"operationId": "services.box.rebuildDockerImage", "security": [{"bearerAuth": []}], "tags": ["Services / Box"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.box.updateDeployScript": {"post": {"operationId": "services.box.updateDeployScript", "security": [{"bearerAuth": []}], "tags": ["Services / Box"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "deployment": {"type": "object", "properties": {"script": {"type": "string", "default": ""}, "token": {"type": "string", "default": "c80667d856342d9daccf"}}, "additionalProperties": false, "default": {}}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.box.updateMounts": {"post": {"operationId": "services.box.updateMounts", "security": [{"bearerAuth": []}], "tags": ["Services / Box"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "mounts": {"type": "array", "items": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "bind"}, "hostPath": {"type": "string", "minLength": 1}, "mountPath": {"type": "string", "minLength": 1}}, "required": ["type", "hostPath", "mount<PERSON>ath"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "volume"}, "name": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "mountPath": {"type": "string", "minLength": 1}}, "required": ["type", "name", "mount<PERSON>ath"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "file"}, "content": {"type": "string"}, "mountPath": {"type": "string", "minLength": 1}}, "required": ["type", "content", "mount<PERSON>ath"], "additionalProperties": false}]}, "default": []}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.box.updateRedirects": {"post": {"operationId": "services.box.updateRedirects", "security": [{"bearerAuth": []}], "tags": ["Services / Box"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "redirects": {"type": "array", "items": {"type": "object", "properties": {"regex": {"type": "string"}, "replacement": {"type": "string"}, "permanent": {"type": "boolean"}, "enabled": {"type": "boolean"}}, "required": ["regex", "replacement", "permanent", "enabled"], "additionalProperties": false}, "default": []}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.box.updateBasicAuth": {"post": {"operationId": "services.box.updateBasicAuth", "security": [{"bearerAuth": []}], "tags": ["Services / Box"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "basicAuth": {"type": "array", "items": {"type": "object", "properties": {"username": {"type": "string"}, "password": {"type": "string"}}, "required": ["username", "password"], "additionalProperties": false}, "default": []}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.box.updateModules": {"post": {"operationId": "services.box.updateModules", "security": [{"bearerAuth": []}], "tags": ["Services / Box"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "modules": {"type": "object", "properties": {"ide": {"type": "boolean", "default": false}, "advanced": {"type": "boolean", "default": false}, "php": {"type": "boolean", "default": false}, "nodejs": {"type": "boolean", "default": false}, "nginx": {"type": "boolean", "default": false}, "python": {"type": "boolean", "default": false}, "ruby": {"type": "boolean", "default": false}, "deployments": {"type": "boolean", "default": false}, "git": {"type": "boolean", "default": false}, "domains": {"type": "boolean", "default": false}, "redirects": {"type": "boolean", "default": false}, "basicAuth": {"type": "boolean", "default": false}, "mounts": {"type": "boolean", "default": false}, "processes": {"type": "boolean", "default": false}, "scripts": {"type": "boolean", "default": false}, "ports": {"type": "boolean", "default": false}, "resources": {"type": "boolean", "default": false}, "env": {"type": "boolean", "default": false}}, "additionalProperties": false, "default": {}}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.box.updateIde": {"post": {"operationId": "services.box.updateIde", "security": [{"bearerAuth": []}], "tags": ["Services / Box"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "ide": {"type": "object", "properties": {"defaultFolder": {"type": "string", "default": "/code"}, "token": {"type": "string", "default": "a8c48bd4a979100aafa9"}, "enabled": {"type": "boolean", "default": true}}, "additionalProperties": false}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.box.updateAdvanced": {"post": {"operationId": "services.box.updateAdvanced", "security": [{"bearerAuth": []}], "tags": ["Services / Box"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "advanced": {"type": "object", "properties": {"buildScript": {"type": "string", "default": ""}, "startScript": {"type": "string", "default": ""}}, "additionalProperties": false}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.box.updatePhp": {"post": {"operationId": "services.box.updatePhp", "security": [{"bearerAuth": []}], "tags": ["Services / Box"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "php": {"type": "object", "properties": {"version": {"type": "string", "default": "8.3"}, "maxUploadSize": {"type": "string", "default": "128M"}, "maxExecutionTime": {"type": "string", "default": "30"}, "opcache": {"type": "boolean", "default": true}, "phpIni": {"type": "string", "default": ""}, "enabled": {"type": "boolean", "default": true}, "ioncube": {"type": "boolean", "default": false}, "sqlsrv": {"type": "boolean", "default": false}}, "additionalProperties": false}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.box.updateNodejs": {"post": {"operationId": "services.box.updateNodejs", "security": [{"bearerAuth": []}], "tags": ["Services / Box"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "nodejs": {"type": "object", "properties": {"version": {"type": "string", "default": "18"}, "yarn": {"type": "boolean", "default": false}, "pnpm": {"type": "boolean", "default": false}, "enabled": {"type": "boolean", "default": true}}, "additionalProperties": false}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.box.updateNginx": {"post": {"operationId": "services.box.updateNginx", "security": [{"bearerAuth": []}], "tags": ["Services / Box"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "nginx": {"type": "object", "properties": {"rootDocument": {"type": "string", "default": "/code"}, "config": {"type": "string", "default": "server {\n\tlisten 80 default_server;\n\tlisten [::]:80 default_server;\n\n\troot {{ document_root }};\n\n\tindex index.php index.html;\n\n\tserver_name _;\n\n\tlocation / {\n\t\ttry_files $uri $uri/ =404;\n\t}\n\n\tlocation ~ .php$ {\n\t\tinclude snippets/fastcgi-php.conf;\n\t\tfastcgi_pass {{ fpm_socket }};\n\t}\n}"}, "enabled": {"type": "boolean", "default": true}}, "additionalProperties": false}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.box.updatePython": {"post": {"operationId": "services.box.updatePython", "security": [{"bearerAuth": []}], "tags": ["Services / Box"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "python": {"type": "object", "properties": {"version": {"type": "string", "default": "3"}, "enabled": {"type": "boolean", "default": false}}, "additionalProperties": false}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.box.updateRuby": {"post": {"operationId": "services.box.updateRuby", "security": [{"bearerAuth": []}], "tags": ["Services / Box"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "ruby": {"type": "object", "properties": {"version": {"type": "string", "default": "2.6"}, "enabled": {"type": "boolean", "default": false}}, "additionalProperties": false}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.box.updateEnv": {"post": {"operationId": "services.box.updateEnv", "security": [{"bearerAuth": []}], "tags": ["Services / Box"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "env": {"type": "object", "properties": {"content": {"type": "string", "default": ""}}, "additionalProperties": false}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.box.runDeployScript": {"post": {"operationId": "services.box.runDeployScript", "security": [{"bearerAuth": []}], "tags": ["Services / Box"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.box.loadPreset": {"post": {"operationId": "services.box.loadPreset", "security": [{"bearerAuth": []}], "tags": ["Services / Box"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "presetKey": {"type": "string"}}, "required": ["projectName", "serviceName", "preset<PERSON>ey"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.box.startService": {"post": {"operationId": "services.box.startService", "security": [{"bearerAuth": []}], "tags": ["Services / Box"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.box.stopService": {"post": {"operationId": "services.box.stopService", "security": [{"bearerAuth": []}], "tags": ["Services / Box"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.box.restartService": {"post": {"operationId": "services.box.restartService", "security": [{"bearerAuth": []}], "tags": ["Services / Box"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.box.updatePorts": {"post": {"operationId": "services.box.updatePorts", "security": [{"bearerAuth": []}], "tags": ["Services / Box"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "ports": {"type": "array", "items": {"type": "object", "properties": {"published": {"type": "number"}, "target": {"type": "number"}, "protocol": {"type": "string", "enum": ["tcp", "udp"], "default": "tcp"}}, "required": ["published", "target"], "additionalProperties": false}, "default": []}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.box.updateResources": {"post": {"operationId": "services.box.updateResources", "security": [{"bearerAuth": []}], "tags": ["Services / Box"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "resources": {"type": "object", "properties": {"memoryReservation": {"type": "number"}, "memoryLimit": {"type": "number"}, "cpuReservation": {"type": "number"}, "cpuLimit": {"type": "number"}}, "required": ["memoryReservation", "memoryLimit", "cpuReservation", "cpuLimit"], "additionalProperties": false}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.createService": {"post": {"operationId": "services.wordpress.createService", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "codeInitialized": {"type": "boolean", "default": false}, "initialVersion": {"type": "string", "default": "latest"}, "git": {"type": "object", "properties": {"name": {"type": "string"}, "email": {"type": "string"}, "url": {"type": "string"}, "branch": {"type": "string"}}, "additionalProperties": false}, "domains": {"type": "array", "items": {"type": "object", "properties": {"host": {"type": "string", "pattern": "^[^\\s*]+$"}, "https": {"type": "boolean", "default": true}, "port": {"type": "number", "default": 80}, "path": {"type": "string", "pattern": "^\\/", "default": "/"}, "middlewares": {"type": "array", "items": {"type": "string"}}, "certificateResolver": {"type": "string"}, "wildcard": {"type": "boolean", "default": false}, "internalProtocol": {"type": "string", "enum": ["http", "https"], "default": "http"}, "service": {"type": "string"}}, "required": ["host"], "additionalProperties": false}, "default": []}, "redirects": {"type": "array", "items": {"type": "object", "properties": {"regex": {"type": "string"}, "replacement": {"type": "string"}, "permanent": {"type": "boolean"}, "enabled": {"type": "boolean"}}, "required": ["regex", "replacement", "permanent", "enabled"], "additionalProperties": false}, "default": []}, "basicAuth": {"type": "array", "items": {"type": "object", "properties": {"username": {"type": "string"}, "password": {"type": "string"}}, "required": ["username", "password"], "additionalProperties": false}, "default": []}, "scripts": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "content": {"type": "string"}, "webhookToken": {"type": "string"}, "schedule": {"type": "string"}, "enabled": {"type": "boolean"}}, "required": ["name", "content", "webhookToken", "enabled"], "additionalProperties": false}, "default": []}, "resources": {"type": "object", "properties": {"memoryReservation": {"type": "number"}, "memoryLimit": {"type": "number"}, "cpuReservation": {"type": "number"}, "cpuLimit": {"type": "number"}}, "required": ["memoryReservation", "memoryLimit", "cpuReservation", "cpuLimit"], "additionalProperties": false}, "ide": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": true}, "defaultFolder": {"type": "string", "default": "/code"}, "token": {"type": "string", "default": "2435a11c3b25a7bdcb33"}}, "additionalProperties": false}, "php": {"type": "object", "properties": {"version": {"type": "string", "default": "8.3"}, "maxUploadSize": {"type": "string", "default": "128M"}, "maxExecutionTime": {"type": "string", "default": "30"}, "opcache": {"type": "boolean", "default": true}, "phpIni": {"type": "string", "default": ""}, "ioncube": {"type": "boolean", "default": false}, "sqlsrv": {"type": "boolean", "default": false}}, "additionalProperties": false}, "nginx": {"type": "object", "properties": {"rootDocument": {"type": "string", "default": "/code"}, "config": {"type": "string", "default": "server {\n\tlisten 80 default_server;\n\tlisten [::]:80 default_server;\n\n\troot {{ document_root }};\n\n\tindex index.php index.html;\n\n\tserver_name _;\n\n\tlocation / {\n\t\ttry_files $uri $uri/ =404;\n\t}\n\n\tlocation ~ .php$ {\n\t\tinclude snippets/fastcgi-php.conf;\n\t\tfastcgi_pass {{ fpm_socket }};\n\t}\n}"}}, "additionalProperties": false}, "env": {"type": "object", "properties": {"content": {"type": "string", "default": ""}}, "additionalProperties": false}, "databaseService": {"type": "string"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.initService": {"post": {"operationId": "services.wordpress.initService", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "git": {"type": "object", "properties": {"name": {"type": "string"}, "email": {"type": "string"}, "url": {"type": "string"}, "branch": {"type": "string"}}, "additionalProperties": false}, "private": {"type": "boolean", "default": true}, "databaseService": {"type": "string"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.runScript": {"post": {"operationId": "services.wordpress.runScript", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "name": {"type": "string"}, "content": {"type": "string"}}, "required": ["projectName", "serviceName", "name", "content"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.updateScripts": {"post": {"operationId": "services.wordpress.updateScripts", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "scripts": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "content": {"type": "string"}, "webhookToken": {"type": "string"}, "schedule": {"type": "string"}, "enabled": {"type": "boolean"}}, "required": ["name", "content", "webhookToken", "enabled"], "additionalProperties": false}, "default": []}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.destroyService": {"post": {"operationId": "services.wordpress.destroyService", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.gitClone": {"post": {"operationId": "services.wordpress.gitClone", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "url": {"type": "string"}, "branch": {"type": "string"}, "private": {"type": "boolean", "default": true}}, "required": ["projectName", "serviceName", "url", "branch"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.updateGitConfig": {"post": {"operationId": "services.wordpress.updateGitConfig", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "name": {"type": "string"}, "email": {"type": "string"}}, "required": ["projectName", "serviceName", "name", "email"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.rebuildDockerImage": {"post": {"operationId": "services.wordpress.rebuildDockerImage", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.updateRedirects": {"post": {"operationId": "services.wordpress.updateRedirects", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "redirects": {"type": "array", "items": {"type": "object", "properties": {"regex": {"type": "string"}, "replacement": {"type": "string"}, "permanent": {"type": "boolean"}, "enabled": {"type": "boolean"}}, "required": ["regex", "replacement", "permanent", "enabled"], "additionalProperties": false}, "default": []}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.updateBasicAuth": {"post": {"operationId": "services.wordpress.updateBasicAuth", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "basicAuth": {"type": "array", "items": {"type": "object", "properties": {"username": {"type": "string"}, "password": {"type": "string"}}, "required": ["username", "password"], "additionalProperties": false}, "default": []}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.updateIde": {"post": {"operationId": "services.wordpress.updateIde", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "ide": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": true}, "defaultFolder": {"type": "string", "default": "/code"}, "token": {"type": "string", "default": "fc63cc9558727d42deec"}}, "additionalProperties": false}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.updatePhp": {"post": {"operationId": "services.wordpress.updatePhp", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "php": {"type": "object", "properties": {"version": {"type": "string", "default": "8.3"}, "maxUploadSize": {"type": "string", "default": "128M"}, "maxExecutionTime": {"type": "string", "default": "30"}, "opcache": {"type": "boolean", "default": true}, "phpIni": {"type": "string", "default": ""}, "ioncube": {"type": "boolean", "default": false}, "sqlsrv": {"type": "boolean", "default": false}}, "additionalProperties": false}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.updateNginx": {"post": {"operationId": "services.wordpress.updateNginx", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "nginx": {"type": "object", "properties": {"rootDocument": {"type": "string", "default": "/code"}, "config": {"type": "string", "default": "server {\n\tlisten 80 default_server;\n\tlisten [::]:80 default_server;\n\n\troot {{ document_root }};\n\n\tindex index.php index.html;\n\n\tserver_name _;\n\n\tlocation / {\n\t\ttry_files $uri $uri/ =404;\n\t}\n\n\tlocation ~ .php$ {\n\t\tinclude snippets/fastcgi-php.conf;\n\t\tfastcgi_pass {{ fpm_socket }};\n\t}\n}"}}, "additionalProperties": false}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.updateEnv": {"post": {"operationId": "services.wordpress.updateEnv", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "env": {"type": "object", "properties": {"content": {"type": "string", "default": ""}}, "additionalProperties": false}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.startService": {"post": {"operationId": "services.wordpress.startService", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.stopService": {"post": {"operationId": "services.wordpress.stopService", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.restartService": {"post": {"operationId": "services.wordpress.restartService", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.updateResources": {"post": {"operationId": "services.wordpress.updateResources", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "resources": {"type": "object", "properties": {"memoryReservation": {"type": "number"}, "memoryLimit": {"type": "number"}, "cpuReservation": {"type": "number"}, "cpuLimit": {"type": "number"}}, "required": ["memoryReservation", "memoryLimit", "cpuReservation", "cpuLimit"], "additionalProperties": false}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.createUser": {"post": {"operationId": "services.wordpress.createUser", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "user": {"type": "object", "properties": {"display_name": {"type": "string"}, "user_email": {"type": "string"}, "roles": {"type": "string"}, "password": {"type": "string"}}, "required": ["display_name", "user_email", "roles", "password"], "additionalProperties": false}}, "required": ["projectName", "serviceName", "user"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.updateUser": {"post": {"operationId": "services.wordpress.updateUser", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "user": {"type": "object", "properties": {"ID": {"type": "number"}, "display_name": {"type": "string"}, "user_email": {"type": "string"}, "roles": {"type": "string"}, "password": {"type": "string"}}, "required": ["ID", "display_name", "user_email", "roles"], "additionalProperties": false}}, "required": ["projectName", "serviceName", "user"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.deleteUser": {"post": {"operationId": "services.wordpress.deleteUser", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "userId": {"type": "number"}}, "required": ["projectName", "serviceName", "userId"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.createRole": {"post": {"operationId": "services.wordpress.createRole", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "role": {"type": "object", "properties": {"name": {"type": "string"}, "display_name": {"type": "string"}}, "required": ["name", "display_name"], "additionalProperties": false}}, "required": ["projectName", "serviceName", "role"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.deleteRole": {"post": {"operationId": "services.wordpress.deleteRole", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "name": {"type": "string"}}, "required": ["projectName", "serviceName", "name"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.updateMaintenanceMode": {"post": {"operationId": "services.wordpress.updateMaintenanceMode", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "enabled": {"type": "boolean"}}, "required": ["projectName", "serviceName", "enabled"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.activateTheme": {"post": {"operationId": "services.wordpress.activateTheme", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "name": {"type": "string"}}, "required": ["projectName", "serviceName", "name"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.installTheme": {"post": {"operationId": "services.wordpress.installTheme", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "name": {"type": "string"}}, "required": ["projectName", "serviceName", "name"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.activatePlugin": {"post": {"operationId": "services.wordpress.activatePlugin", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "name": {"type": "string"}}, "required": ["projectName", "serviceName", "name"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.deactivatePlugin": {"post": {"operationId": "services.wordpress.deactivatePlugin", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "name": {"type": "string"}}, "required": ["projectName", "serviceName", "name"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.installPlugin": {"post": {"operationId": "services.wordpress.installPlugin", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "name": {"type": "string"}}, "required": ["projectName", "serviceName", "name"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.createOption": {"post": {"operationId": "services.wordpress.createOption", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "option": {"type": "object", "properties": {"name": {"type": "string"}, "value": {"type": "string"}}, "required": ["name", "value"], "additionalProperties": false}}, "required": ["projectName", "serviceName", "option"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.updateOption": {"post": {"operationId": "services.wordpress.updateOption", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "option": {"type": "object", "properties": {"name": {"type": "string"}, "value": {"type": "string"}}, "required": ["name", "value"], "additionalProperties": false}}, "required": ["projectName", "serviceName", "option"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.deleteOption": {"post": {"operationId": "services.wordpress.deleteOption", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "name": {"type": "string"}}, "required": ["projectName", "serviceName", "name"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.mediaRegenerate": {"post": {"operationId": "services.wordpress.mediaRegenerate", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.flushCache": {"post": {"operationId": "services.wordpress.flushCache", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.deleteTransient": {"post": {"operationId": "services.wordpress.deleteTransient", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.dbOptimize": {"post": {"operationId": "services.wordpress.dbOptimize", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.searchReplace": {"post": {"operationId": "services.wordpress.searchReplace", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "search": {"type": "string"}, "replace": {"type": "string"}}, "required": ["projectName", "serviceName", "search", "replace"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.searchReplaceDryRun": {"post": {"operationId": "services.wordpress.searchReplaceDryRun", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "search": {"type": "string"}, "replace": {"type": "string"}}, "required": ["projectName", "serviceName", "search", "replace"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.updateWpCore": {"post": {"operationId": "services.wordpress.updateWpCore", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.wordpress.updateWpConfig": {"post": {"operationId": "services.wordpress.updateWpConfig", "security": [{"bearerAuth": []}], "tags": ["Services / Wordpress"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "content": {"type": "string"}}, "required": ["projectName", "serviceName", "content"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mariadb.createService": {"post": {"operationId": "services.mariadb.createService", "security": [{"bearerAuth": []}], "tags": ["Services / Mariadb"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "databaseName": {"type": "string", "pattern": "^[a-zA-Z][a-zA-Z0-9_]{0,62}$"}, "user": {"type": "string", "pattern": "^[a-zA-Z][a-zA-Z0-9_]{0,62}$"}, "image": {"type": "string", "default": "mariadb:11"}, "exposedPort": {"type": "number"}, "password": {"type": "string", "default": "6a1f699e90ceb5f75342"}, "rootPassword": {"$ref": "#/properties/json/properties/password"}, "resources": {"type": "object", "properties": {"memoryReservation": {"type": "number"}, "memoryLimit": {"type": "number"}, "cpuReservation": {"type": "number"}, "cpuLimit": {"type": "number"}}, "required": ["memoryReservation", "memoryLimit", "cpuReservation", "cpuLimit"], "additionalProperties": false}, "backup": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "schedule": {"type": "string", "minLength": 1}, "destinationId": {"type": "string", "minLength": 1}, "prefix": {"type": "string", "pattern": "^[\\w-/.]+$"}, "databaseName": {"type": "string"}}, "required": ["enabled", "schedule", "destinationId", "prefix"], "additionalProperties": false}, "env": {"type": "string"}, "configFile": {"type": "string"}, "command": {"type": "string"}, "phpMyAdmin": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}, "token": {"type": "string", "default": "530aca3685a75bce4985"}}, "additionalProperties": false}, "dbGate": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}, "token": {"type": "string", "default": "5fd41b15891b7ff79715"}}, "additionalProperties": false}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mariadb.exposeService": {"post": {"operationId": "services.mariadb.exposeService", "security": [{"bearerAuth": []}], "tags": ["Services / Mariadb"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "exposedPort": {"type": "number", "minimum": 0, "maximum": 65535}}, "required": ["projectName", "serviceName", "exposedPort"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mariadb.updateResources": {"post": {"operationId": "services.mariadb.updateResources", "security": [{"bearerAuth": []}], "tags": ["Services / Mariadb"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "resources": {"type": "object", "properties": {"memoryReservation": {"type": "number"}, "memoryLimit": {"type": "number"}, "cpuReservation": {"type": "number"}, "cpuLimit": {"type": "number"}}, "required": ["memoryReservation", "memoryLimit", "cpuReservation", "cpuLimit"], "additionalProperties": false}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mariadb.enableService": {"post": {"operationId": "services.mariadb.enableService", "security": [{"bearerAuth": []}], "tags": ["Services / Mariadb"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mariadb.disableService": {"post": {"operationId": "services.mariadb.disableService", "security": [{"bearerAuth": []}], "tags": ["Services / Mariadb"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mariadb.destroyService": {"post": {"operationId": "services.mariadb.destroyService", "security": [{"bearerAuth": []}], "tags": ["Services / Mariadb"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mariadb.enablePhpMyAdmin": {"post": {"operationId": "services.mariadb.enablePhpMyAdmin", "security": [{"bearerAuth": []}], "tags": ["Services / Mariadb"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mariadb.disablePhpMyAdmin": {"post": {"operationId": "services.mariadb.disablePhpMyAdmin", "security": [{"bearerAuth": []}], "tags": ["Services / Mariadb"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mariadb.enableDbGate": {"post": {"operationId": "services.mariadb.enableDbGate", "security": [{"bearerAuth": []}], "tags": ["Services / Mariadb"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mariadb.disableDbGate": {"post": {"operationId": "services.mariadb.disableDbGate", "security": [{"bearerAuth": []}], "tags": ["Services / Mariadb"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mariadb.updateAdvanced": {"post": {"operationId": "services.mariadb.updateAdvanced", "security": [{"bearerAuth": []}], "tags": ["Services / Mariadb"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "image": {"type": "string"}, "command": {"type": "string"}, "env": {"type": "string"}, "configFile": {"type": "string"}}, "required": ["projectName", "serviceName", "image", "command"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mariadb.updateCredentials": {"post": {"operationId": "services.mariadb.updateCredentials", "security": [{"bearerAuth": []}], "tags": ["Services / Mariadb"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "password": {"type": "string"}, "rootPassword": {"type": "string"}}, "required": ["projectName", "serviceName", "password", "rootPassword"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mongo.createService": {"post": {"operationId": "services.mongo.createService", "security": [{"bearerAuth": []}], "tags": ["Services / Mongo"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "user": {"type": "string", "pattern": "^[a-zA-Z][a-zA-Z0-9_]{0,62}$"}, "image": {"type": "string", "default": "mongo:8"}, "exposedPort": {"type": "number"}, "password": {"type": "string", "default": "35694dbeca90844ba1bc"}, "backup": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "schedule": {"type": "string", "minLength": 1}, "destinationId": {"type": "string", "minLength": 1}, "prefix": {"type": "string", "pattern": "^[\\w-/.]+$"}, "databaseName": {"type": "string"}}, "required": ["enabled", "schedule", "destinationId", "prefix"], "additionalProperties": false}, "resources": {"type": "object", "properties": {"memoryReservation": {"type": "number"}, "memoryLimit": {"type": "number"}, "cpuReservation": {"type": "number"}, "cpuLimit": {"type": "number"}}, "required": ["memoryReservation", "memoryLimit", "cpuReservation", "cpuLimit"], "additionalProperties": false}, "env": {"type": "string"}, "command": {"type": "string"}, "mongoExpress": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}, "token": {"type": "string", "default": "1b69584e1b3a1da4228e"}}, "additionalProperties": false}, "dbGate": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}, "token": {"type": "string", "default": "9bd2c71d3af5b5cc4572"}}, "additionalProperties": false}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mongo.exposeService": {"post": {"operationId": "services.mongo.exposeService", "security": [{"bearerAuth": []}], "tags": ["Services / Mongo"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "exposedPort": {"type": "number", "minimum": 0, "maximum": 65535}}, "required": ["projectName", "serviceName", "exposedPort"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mongo.updateResources": {"post": {"operationId": "services.mongo.updateResources", "security": [{"bearerAuth": []}], "tags": ["Services / Mongo"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "resources": {"type": "object", "properties": {"memoryReservation": {"type": "number"}, "memoryLimit": {"type": "number"}, "cpuReservation": {"type": "number"}, "cpuLimit": {"type": "number"}}, "required": ["memoryReservation", "memoryLimit", "cpuReservation", "cpuLimit"], "additionalProperties": false}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mongo.enableService": {"post": {"operationId": "services.mongo.enableService", "security": [{"bearerAuth": []}], "tags": ["Services / Mongo"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mongo.disableService": {"post": {"operationId": "services.mongo.disableService", "security": [{"bearerAuth": []}], "tags": ["Services / Mongo"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mongo.destroyService": {"post": {"operationId": "services.mongo.destroyService", "security": [{"bearerAuth": []}], "tags": ["Services / Mongo"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mongo.enableMongoExpress": {"post": {"operationId": "services.mongo.enableMongoExpress", "security": [{"bearerAuth": []}], "tags": ["Services / Mongo"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mongo.disableMongoExpress": {"post": {"operationId": "services.mongo.disableMongoExpress", "security": [{"bearerAuth": []}], "tags": ["Services / Mongo"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mongo.enableDbGate": {"post": {"operationId": "services.mongo.enableDbGate", "security": [{"bearerAuth": []}], "tags": ["Services / Mongo"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mongo.disableDbGate": {"post": {"operationId": "services.mongo.disableDbGate", "security": [{"bearerAuth": []}], "tags": ["Services / Mongo"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mongo.updateAdvanced": {"post": {"operationId": "services.mongo.updateAdvanced", "security": [{"bearerAuth": []}], "tags": ["Services / Mongo"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "image": {"type": "string"}, "command": {"type": "string"}, "env": {"type": "string"}}, "required": ["projectName", "serviceName", "image", "command"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mongo.updateCredentials": {"post": {"operationId": "services.mongo.updateCredentials", "security": [{"bearerAuth": []}], "tags": ["Services / Mongo"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "password": {"type": "string"}}, "required": ["projectName", "serviceName", "password"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mysql.createService": {"post": {"operationId": "services.mysql.createService", "security": [{"bearerAuth": []}], "tags": ["Services / Mysql"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "databaseName": {"type": "string", "pattern": "^[a-zA-Z][a-zA-Z0-9_]{0,62}$"}, "user": {"type": "string", "pattern": "^[a-zA-Z][a-zA-Z0-9_]{0,62}$"}, "image": {"type": "string", "default": "mysql:9"}, "exposedPort": {"type": "number"}, "password": {"type": "string", "default": "82d5f928af6158e0c6eb"}, "rootPassword": {"$ref": "#/properties/json/properties/password"}, "resources": {"type": "object", "properties": {"memoryReservation": {"type": "number"}, "memoryLimit": {"type": "number"}, "cpuReservation": {"type": "number"}, "cpuLimit": {"type": "number"}}, "required": ["memoryReservation", "memoryLimit", "cpuReservation", "cpuLimit"], "additionalProperties": false}, "backup": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "schedule": {"type": "string", "minLength": 1}, "destinationId": {"type": "string", "minLength": 1}, "prefix": {"type": "string", "pattern": "^[\\w-/.]+$"}, "databaseName": {"type": "string"}}, "required": ["enabled", "schedule", "destinationId", "prefix"], "additionalProperties": false}, "env": {"type": "string"}, "configFile": {"type": "string"}, "command": {"type": "string"}, "phpMyAdmin": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}, "token": {"type": "string", "default": "989243cb47dd19c5fcd4"}}, "additionalProperties": false}, "dbGate": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}, "token": {"type": "string", "default": "e7c2c6d49d8637442cd4"}}, "additionalProperties": false}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mysql.exposeService": {"post": {"operationId": "services.mysql.exposeService", "security": [{"bearerAuth": []}], "tags": ["Services / Mysql"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "exposedPort": {"type": "number", "minimum": 0, "maximum": 65535}}, "required": ["projectName", "serviceName", "exposedPort"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mysql.updateResources": {"post": {"operationId": "services.mysql.updateResources", "security": [{"bearerAuth": []}], "tags": ["Services / Mysql"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "resources": {"type": "object", "properties": {"memoryReservation": {"type": "number"}, "memoryLimit": {"type": "number"}, "cpuReservation": {"type": "number"}, "cpuLimit": {"type": "number"}}, "required": ["memoryReservation", "memoryLimit", "cpuReservation", "cpuLimit"], "additionalProperties": false}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mysql.enableService": {"post": {"operationId": "services.mysql.enableService", "security": [{"bearerAuth": []}], "tags": ["Services / Mysql"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mysql.disableService": {"post": {"operationId": "services.mysql.disableService", "security": [{"bearerAuth": []}], "tags": ["Services / Mysql"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mysql.destroyService": {"post": {"operationId": "services.mysql.destroyService", "security": [{"bearerAuth": []}], "tags": ["Services / Mysql"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mysql.enablePhpMyAdmin": {"post": {"operationId": "services.mysql.enablePhpMyAdmin", "security": [{"bearerAuth": []}], "tags": ["Services / Mysql"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mysql.disablePhpMyAdmin": {"post": {"operationId": "services.mysql.disablePhpMyAdmin", "security": [{"bearerAuth": []}], "tags": ["Services / Mysql"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mysql.enableDbGate": {"post": {"operationId": "services.mysql.enableDbGate", "security": [{"bearerAuth": []}], "tags": ["Services / Mysql"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mysql.disableDbGate": {"post": {"operationId": "services.mysql.disableDbGate", "security": [{"bearerAuth": []}], "tags": ["Services / Mysql"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mysql.updateAdvanced": {"post": {"operationId": "services.mysql.updateAdvanced", "security": [{"bearerAuth": []}], "tags": ["Services / Mysql"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "image": {"type": "string"}, "command": {"type": "string"}, "env": {"type": "string"}, "configFile": {"type": "string"}}, "required": ["projectName", "serviceName", "image", "command"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.mysql.updateCredentials": {"post": {"operationId": "services.mysql.updateCredentials", "security": [{"bearerAuth": []}], "tags": ["Services / Mysql"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "password": {"type": "string"}, "rootPassword": {"type": "string"}}, "required": ["projectName", "serviceName", "password", "rootPassword"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.postgres.createService": {"post": {"operationId": "services.postgres.createService", "security": [{"bearerAuth": []}], "tags": ["Services / Postgres"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "databaseName": {"type": "string", "pattern": "^[a-zA-Z][a-zA-Z0-9_]{0,62}$"}, "user": {"type": "string", "pattern": "^[a-zA-Z][a-zA-Z0-9_]{0,62}$"}, "image": {"type": "string", "default": "postgres:17"}, "exposedPort": {"type": "number"}, "password": {"type": "string", "default": "38f16d05ed1571970f23"}, "backup": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "schedule": {"type": "string", "minLength": 1}, "destinationId": {"type": "string", "minLength": 1}, "prefix": {"type": "string", "pattern": "^[\\w-/.]+$"}, "databaseName": {"type": "string"}}, "required": ["enabled", "schedule", "destinationId", "prefix"], "additionalProperties": false}, "resources": {"type": "object", "properties": {"memoryReservation": {"type": "number"}, "memoryLimit": {"type": "number"}, "cpuReservation": {"type": "number"}, "cpuLimit": {"type": "number"}}, "required": ["memoryReservation", "memoryLimit", "cpuReservation", "cpuLimit"], "additionalProperties": false}, "env": {"type": "string"}, "command": {"type": "string"}, "pgWeb": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}, "token": {"type": "string", "default": "fabac5bef5be2197dd3a"}}, "additionalProperties": false}, "dbGate": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}, "token": {"type": "string", "default": "acf911615a0ae348d01d"}}, "additionalProperties": false}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.postgres.exposeService": {"post": {"operationId": "services.postgres.exposeService", "security": [{"bearerAuth": []}], "tags": ["Services / Postgres"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "exposedPort": {"type": "number", "minimum": 0, "maximum": 65535}}, "required": ["projectName", "serviceName", "exposedPort"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.postgres.updateResources": {"post": {"operationId": "services.postgres.updateResources", "security": [{"bearerAuth": []}], "tags": ["Services / Postgres"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "resources": {"type": "object", "properties": {"memoryReservation": {"type": "number"}, "memoryLimit": {"type": "number"}, "cpuReservation": {"type": "number"}, "cpuLimit": {"type": "number"}}, "required": ["memoryReservation", "memoryLimit", "cpuReservation", "cpuLimit"], "additionalProperties": false}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.postgres.enableService": {"post": {"operationId": "services.postgres.enableService", "security": [{"bearerAuth": []}], "tags": ["Services / Postgres"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.postgres.disableService": {"post": {"operationId": "services.postgres.disableService", "security": [{"bearerAuth": []}], "tags": ["Services / Postgres"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.postgres.destroyService": {"post": {"operationId": "services.postgres.destroyService", "security": [{"bearerAuth": []}], "tags": ["Services / Postgres"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.postgres.enablePgWeb": {"post": {"operationId": "services.postgres.enablePgWeb", "security": [{"bearerAuth": []}], "tags": ["Services / Postgres"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.postgres.disablePgWeb": {"post": {"operationId": "services.postgres.disablePgWeb", "security": [{"bearerAuth": []}], "tags": ["Services / Postgres"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.postgres.enableDbGate": {"post": {"operationId": "services.postgres.enableDbGate", "security": [{"bearerAuth": []}], "tags": ["Services / Postgres"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.postgres.disableDbGate": {"post": {"operationId": "services.postgres.disableDbGate", "security": [{"bearerAuth": []}], "tags": ["Services / Postgres"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.postgres.updateAdvanced": {"post": {"operationId": "services.postgres.updateAdvanced", "security": [{"bearerAuth": []}], "tags": ["Services / Postgres"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "image": {"type": "string"}, "command": {"type": "string"}, "env": {"type": "string"}}, "required": ["projectName", "serviceName", "image", "command"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.postgres.updateCredentials": {"post": {"operationId": "services.postgres.updateCredentials", "security": [{"bearerAuth": []}], "tags": ["Services / Postgres"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "password": {"type": "string"}}, "required": ["projectName", "serviceName", "password"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.redis.createService": {"post": {"operationId": "services.redis.createService", "security": [{"bearerAuth": []}], "tags": ["Services / Redis"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "image": {"type": "string", "default": "redis:7"}, "exposedPort": {"type": "number"}, "password": {"type": "string", "default": "878e92264feefd7dc8ed"}, "resources": {"type": "object", "properties": {"memoryReservation": {"type": "number"}, "memoryLimit": {"type": "number"}, "cpuReservation": {"type": "number"}, "cpuLimit": {"type": "number"}}, "required": ["memoryReservation", "memoryLimit", "cpuReservation", "cpuLimit"], "additionalProperties": false}, "env": {"type": "string"}, "command": {"type": "string"}, "redisCommander": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}, "token": {"type": "string", "default": "6e82ddc9d68f95b81fce"}}, "additionalProperties": false}, "dbGate": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}, "token": {"type": "string", "default": "706f8cc18763521b3633"}}, "additionalProperties": false}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.redis.exposeService": {"post": {"operationId": "services.redis.exposeService", "security": [{"bearerAuth": []}], "tags": ["Services / Redis"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "exposedPort": {"type": "number", "minimum": 0, "maximum": 65535}}, "required": ["projectName", "serviceName", "exposedPort"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.redis.updateResources": {"post": {"operationId": "services.redis.updateResources", "security": [{"bearerAuth": []}], "tags": ["Services / Redis"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "resources": {"type": "object", "properties": {"memoryReservation": {"type": "number"}, "memoryLimit": {"type": "number"}, "cpuReservation": {"type": "number"}, "cpuLimit": {"type": "number"}}, "required": ["memoryReservation", "memoryLimit", "cpuReservation", "cpuLimit"], "additionalProperties": false}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.redis.enableService": {"post": {"operationId": "services.redis.enableService", "security": [{"bearerAuth": []}], "tags": ["Services / Redis"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.redis.disableService": {"post": {"operationId": "services.redis.disableService", "security": [{"bearerAuth": []}], "tags": ["Services / Redis"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.redis.destroyService": {"post": {"operationId": "services.redis.destroyService", "security": [{"bearerAuth": []}], "tags": ["Services / Redis"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.redis.enableRedisCommander": {"post": {"operationId": "services.redis.enableRedisCommander", "security": [{"bearerAuth": []}], "tags": ["Services / Redis"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.redis.disableRedisCommander": {"post": {"operationId": "services.redis.disableRedisCommander", "security": [{"bearerAuth": []}], "tags": ["Services / Redis"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.redis.enableDbGate": {"post": {"operationId": "services.redis.enableDbGate", "security": [{"bearerAuth": []}], "tags": ["Services / Redis"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.redis.disableDbGate": {"post": {"operationId": "services.redis.disableDbGate", "security": [{"bearerAuth": []}], "tags": ["Services / Redis"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.redis.updateAdvanced": {"post": {"operationId": "services.redis.updateAdvanced", "security": [{"bearerAuth": []}], "tags": ["Services / Redis"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "image": {"type": "string"}, "command": {"type": "string"}, "env": {"type": "string"}}, "required": ["projectName", "serviceName", "image", "command"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.redis.updateCredentials": {"post": {"operationId": "services.redis.updateCredentials", "security": [{"bearerAuth": []}], "tags": ["Services / Redis"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "password": {"type": "string"}}, "required": ["projectName", "serviceName", "password"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.compose.createService": {"post": {"operationId": "services.compose.createService", "security": [{"bearerAuth": []}], "tags": ["Services / Compose"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "source": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "inline"}, "content": {"type": "string"}}, "required": ["type", "content"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "git"}, "repo": {"type": "string"}, "ref": {"type": "string"}, "rootPath": {"type": "string"}, "composeFile": {"type": "string"}}, "required": ["type", "repo", "ref", "rootPath", "composeFile"], "additionalProperties": false}]}, "env": {"type": "string", "default": ""}, "createDotEnv": {"type": "boolean", "default": false}, "domains": {"type": "array", "items": {"type": "object", "properties": {"host": {"type": "string", "pattern": "^[^\\s*]+$"}, "https": {"type": "boolean", "default": true}, "port": {"type": "number", "default": 80}, "path": {"type": "string", "pattern": "^\\/", "default": "/"}, "middlewares": {"type": "array", "items": {"type": "string"}}, "certificateResolver": {"type": "string"}, "wildcard": {"type": "boolean", "default": false}, "internalProtocol": {"type": "string", "enum": ["http", "https"], "default": "http"}, "service": {"type": "string"}}, "required": ["host"], "additionalProperties": false}, "default": []}, "redirects": {"type": "array", "items": {"type": "object", "properties": {"regex": {"type": "string"}, "replacement": {"type": "string"}, "permanent": {"type": "boolean"}, "enabled": {"type": "boolean"}}, "required": ["regex", "replacement", "permanent", "enabled"], "additionalProperties": false}}, "basicAuth": {"type": "array", "items": {"type": "object", "properties": {"username": {"type": "string"}, "password": {"type": "string"}}, "required": ["username", "password"], "additionalProperties": false}}, "maintenance": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "title": {"type": "string"}, "subtitle": {"type": "string"}, "customLogo": {"type": "string"}, "customCss": {"type": "string"}, "hideLogo": {"type": "boolean"}, "hideLinks": {"type": "boolean"}}, "required": ["enabled"], "additionalProperties": false}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.compose.updateEnv": {"post": {"operationId": "services.compose.updateEnv", "security": [{"bearerAuth": []}], "tags": ["Services / Compose"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "env": {"type": "string", "default": ""}, "createDotEnv": {"type": "boolean"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.compose.refreshDeployToken": {"post": {"operationId": "services.compose.refreshDeployToken", "security": [{"bearerAuth": []}], "tags": ["Services / Compose"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.compose.updateSourceInline": {"post": {"operationId": "services.compose.updateSourceInline", "security": [{"bearerAuth": []}], "tags": ["Services / Compose"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "content": {"type": "string", "minLength": 1}}, "required": ["projectName", "serviceName", "content"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.compose.updateSourceGit": {"post": {"operationId": "services.compose.updateSourceGit", "security": [{"bearerAuth": []}], "tags": ["Services / Compose"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "repo": {"type": "string", "minLength": 1}, "ref": {"type": "string", "minLength": 1}, "rootPath": {"type": "string", "pattern": "^\\/"}, "composeFile": {"type": "string"}}, "required": ["projectName", "serviceName", "repo", "ref", "rootPath", "composeFile"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.compose.updateRedirects": {"post": {"operationId": "services.compose.updateRedirects", "security": [{"bearerAuth": []}], "tags": ["Services / Compose"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "redirects": {"type": "array", "items": {"type": "object", "properties": {"regex": {"type": "string"}, "replacement": {"type": "string"}, "permanent": {"type": "boolean"}, "enabled": {"type": "boolean"}}, "required": ["regex", "replacement", "permanent", "enabled"], "additionalProperties": false}}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.compose.updateBasicAuth": {"post": {"operationId": "services.compose.updateBasicAuth", "security": [{"bearerAuth": []}], "tags": ["Services / Compose"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "basicAuth": {"type": "array", "items": {"type": "object", "properties": {"username": {"type": "string"}, "password": {"type": "string"}}, "required": ["username", "password"], "additionalProperties": false}}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.compose.updateMaintenance": {"post": {"operationId": "services.compose.updateMaintenance", "security": [{"bearerAuth": []}], "tags": ["Services / Compose"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "maintenance": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "title": {"type": "string"}, "subtitle": {"type": "string"}, "customLogo": {"type": "string"}, "customCss": {"type": "string"}, "hideLogo": {"type": "boolean"}, "hideLinks": {"type": "boolean"}}, "required": ["enabled"], "additionalProperties": false}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.compose.deployService": {"post": {"operationId": "services.compose.deployService", "security": [{"bearerAuth": []}], "tags": ["Services / Compose"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "forceRebuild": {"type": "boolean", "default": false}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.compose.destroyService": {"post": {"operationId": "services.compose.destroyService", "security": [{"bearerAuth": []}], "tags": ["Services / Compose"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.compose.startService": {"post": {"operationId": "services.compose.startService", "security": [{"bearerAuth": []}], "tags": ["Services / Compose"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.compose.stopService": {"post": {"operationId": "services.compose.stopService", "security": [{"bearerAuth": []}], "tags": ["Services / Compose"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/services.compose.restartService": {"post": {"operationId": "services.compose.restartService", "security": [{"bearerAuth": []}], "tags": ["Services / Compose"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/auth.login": {"post": {"operationId": "auth.login", "security": [{"bearerAuth": []}], "tags": ["<PERSON><PERSON>"], "description": "Get a session token to access the panel", "summary": "Get a session token", "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "password": {"type": "string"}, "rememberMe": {"type": "boolean", "default": false}, "code": {"type": "string"}}, "required": ["email", "password"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/auth.logout": {"post": {"operationId": "auth.logout", "security": [{"bearerAuth": []}], "tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {}}}}}, "/api/trpc/portalLicense.activate": {"post": {"operationId": "portalLicense.activate", "security": [{"bearerAuth": []}], "tags": ["Portal License"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {}}}}}, "/api/trpc/portalLicense.deactivate": {"post": {"operationId": "portalLicense.deactivate", "security": [{"bearerAuth": []}], "tags": ["Portal License"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {}}}}}, "/api/trpc/lemonLicense.activateByOrder": {"post": {"operationId": "lemonLicense.activateByOrder", "security": [{"bearerAuth": []}], "tags": ["Lemon License"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"orderId": {"type": "string"}, "identifier": {"type": "string"}}, "required": ["orderId", "identifier"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/lemonLicense.activate": {"post": {"operationId": "lemonLicense.activate", "security": [{"bearerAuth": []}], "tags": ["Lemon License"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"licenseKey": {"type": "string"}}, "required": ["licenseKey"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/lemonLicense.deactivate": {"post": {"operationId": "lemonLicense.deactivate", "security": [{"bearerAuth": []}], "tags": ["Lemon License"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {}}}}}, "/api/trpc/update.update": {"post": {"operationId": "update.update", "security": [{"bearerAuth": []}], "tags": ["Update"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {}}}}}, "/api/trpc/setup.setup": {"post": {"operationId": "setup.setup", "security": [{"bearerAuth": []}], "tags": ["Setup"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "password": {"type": "string", "minLength": 8}, "subscribe": {"type": "boolean"}, "source": {"type": "string", "minLength": 1}, "terms": {"type": "boolean"}}, "required": ["email", "password", "subscribe", "source", "terms"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/settings.setGithubToken": {"post": {"operationId": "settings.setGithubToken", "security": [{"bearerAuth": []}], "tags": ["Settings"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"githubToken": {"type": "string", "default": ""}}, "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/settings.setDailyDockerCleanup": {"post": {"operationId": "settings.setDailyDockerCleanup", "security": [{"bearerAuth": []}], "tags": ["Settings"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"dailyDockerCleanup": {"type": "boolean"}}, "required": ["dailyDockerCleanup"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/settings.setLetsEncryptEmail": {"post": {"operationId": "settings.setLetsEncryptEmail", "security": [{"bearerAuth": []}], "tags": ["Settings"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"letsEncryptEmail": {"type": "string", "format": "email"}}, "required": ["letsEncryptEmail"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/settings.restartEasypanel": {"post": {"operationId": "settings.restartEasypanel", "security": [{"bearerAuth": []}], "tags": ["Settings"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {}}}}}, "/api/trpc/settings.cleanupDockerImages": {"post": {"operationId": "settings.cleanupDockerImages", "security": [{"bearerAuth": []}], "tags": ["Settings"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {}}}}}, "/api/trpc/settings.cleanupDockerBuilder": {"post": {"operationId": "settings.cleanupDockerBuilder", "security": [{"bearerAuth": []}], "tags": ["Settings"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {}}}}}, "/api/trpc/settings.systemPrune": {"post": {"operationId": "settings.systemPrune", "security": [{"bearerAuth": []}], "tags": ["Settings"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {}}}}}, "/api/trpc/settings.setPanelDomain": {"post": {"operationId": "settings.setPanelDomain", "security": [{"bearerAuth": []}], "tags": ["Settings"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"serveOnIp": {"type": "boolean"}, "panelDomain": {"anyOf": [{"type": "null"}, {"type": "string", "const": ""}, {"type": "string", "pattern": "^[^\\s*]+$"}]}}, "required": ["serveOnIp", "panelDomain"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/settings.changeCredentials": {"post": {"operationId": "settings.changeCredentials", "security": [{"bearerAuth": []}], "tags": ["Settings"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "oldPassword": {"type": "string", "minLength": 8}, "newPassword": {"type": "string", "minLength": 8}}, "required": ["email", "oldPassword", "newPassword"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/settings.refreshServerIp": {"post": {"operationId": "settings.refreshServerIp", "security": [{"bearerAuth": []}], "tags": ["Settings"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {}}}}}, "/api/trpc/settings.checkForUpdates": {"post": {"operationId": "settings.checkForUpdates", "security": [{"bearerAuth": []}], "tags": ["Settings"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {}}}}}, "/api/trpc/settings.setGoogleAnalyticsMeasurementId": {"post": {"operationId": "settings.setGoogleAnalyticsMeasurementId", "security": [{"bearerAuth": []}], "tags": ["Settings"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"measurementId": {"type": "string", "default": ""}}, "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/templates.createFromSchema": {"post": {"operationId": "templates.createFromSchema", "security": [{"bearerAuth": []}], "tags": ["Templates"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"name": {"type": "string", "default": "Unknown"}, "projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "schema": {"type": "object", "properties": {"services": {"type": "array", "items": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "app"}, "data": {"type": "object", "properties": {"serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "source": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "image"}, "image": {"type": "string"}, "username": {"type": "string"}, "password": {"type": "string"}}, "required": ["type", "image"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "github"}, "owner": {"type": "string", "minLength": 1}, "repo": {"type": "string", "minLength": 1}, "ref": {"type": "string", "minLength": 1}, "path": {"type": "string", "pattern": "^\\/"}, "autoDeploy": {"type": "boolean"}}, "required": ["type", "owner", "repo", "ref", "path", "autoDeploy"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "git"}, "repo": {"type": "string", "minLength": 1}, "ref": {"type": "string", "minLength": 1}, "path": {"type": "string", "pattern": "^\\/"}}, "required": ["type", "repo", "ref", "path"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "upload"}}, "required": ["type"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "dockerfile"}, "dockerfile": {"type": "string", "minLength": 1}}, "required": ["type", "dockerfile"], "additionalProperties": false}]}, "build": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "dockerfile"}, "file": {"type": "string"}}, "required": ["type"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "buildpacks"}, "buildpacksBuilder": {"type": "string", "default": "heroku/builder:24"}}, "required": ["type"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "heroku-buildpacks"}}, "required": ["type"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "paketo-buildpacks"}}, "required": ["type"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "nixpacks"}, "nixpacksVersion": {"type": "string", "default": "1.34.1"}, "installCommand": {"type": "string"}, "buildCommand": {"type": "string"}, "startCommand": {"type": "string"}, "nixPackages": {"type": "string"}, "aptPackages": {"type": "string"}}, "required": ["type"], "additionalProperties": false}]}, "env": {"type": "string", "default": ""}, "basicAuth": {"type": "array", "items": {"type": "object", "properties": {"username": {"type": "string"}, "password": {"type": "string"}}, "required": ["username", "password"], "additionalProperties": false}}, "deploy": {"type": "object", "properties": {"replicas": {"type": "number", "default": 1}, "command": {"type": ["string", "null"], "default": null}, "zeroDowntime": {"type": "boolean", "default": true}, "capAdd": {"type": "array", "items": {"type": "string"}}, "capDrop": {"type": "array", "items": {"type": "string"}}, "sysctls": {"type": "object", "additionalProperties": {"type": "string"}}, "groups": {"type": "array", "items": {"type": "string"}}, "tiniInit": {"type": "boolean"}}, "additionalProperties": false, "default": {}}, "domains": {"type": "array", "items": {"type": "object", "properties": {"host": {"type": "string", "pattern": "^[^\\s*]+$"}, "https": {"type": "boolean", "default": true}, "port": {"type": "number", "default": 80}, "path": {"type": "string", "pattern": "^\\/", "default": "/"}, "middlewares": {"type": "array", "items": {"type": "string"}}, "certificateResolver": {"type": "string"}, "wildcard": {"type": "boolean", "default": false}, "internalProtocol": {"type": "string", "enum": ["http", "https"], "default": "http"}, "service": {"type": "string"}}, "required": ["host"], "additionalProperties": false}, "default": []}, "mounts": {"type": "array", "items": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "bind"}, "hostPath": {"type": "string", "minLength": 1}, "mountPath": {"type": "string", "minLength": 1}}, "required": ["type", "hostPath", "mount<PERSON>ath"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "volume"}, "name": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "mountPath": {"type": "string", "minLength": 1}}, "required": ["type", "name", "mount<PERSON>ath"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "file"}, "content": {"type": "string"}, "mountPath": {"type": "string", "minLength": 1}}, "required": ["type", "content", "mount<PERSON>ath"], "additionalProperties": false}]}, "default": []}, "ports": {"type": "array", "items": {"type": "object", "properties": {"published": {"type": "number"}, "target": {"type": "number"}, "protocol": {"type": "string", "enum": ["tcp", "udp"], "default": "tcp"}}, "required": ["published", "target"], "additionalProperties": false}, "default": []}, "resources": {"type": "object", "properties": {"memoryReservation": {"type": "number"}, "memoryLimit": {"type": "number"}, "cpuReservation": {"type": "number"}, "cpuLimit": {"type": "number"}}, "required": ["memoryReservation", "memoryLimit", "cpuReservation", "cpuLimit"], "additionalProperties": false}, "maintenance": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "title": {"type": "string"}, "subtitle": {"type": "string"}, "customLogo": {"type": "string"}, "customCss": {"type": "string"}, "hideLogo": {"type": "boolean"}, "hideLinks": {"type": "boolean"}}, "required": ["enabled"], "additionalProperties": false}}, "required": ["serviceName"], "additionalProperties": false}}, "required": ["type", "data"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "mysql"}, "data": {"type": "object", "properties": {"serviceName": {"$ref": "#/properties/json/properties/schema/properties/services/items/anyOf/0/properties/data/properties/serviceName"}, "databaseName": {"type": "string", "pattern": "^[a-zA-Z][a-zA-Z0-9_]{0,62}$"}, "user": {"type": "string", "pattern": "^[a-zA-Z][a-zA-Z0-9_]{0,62}$"}, "image": {"type": "string", "default": "mysql:9"}, "exposedPort": {"type": "number"}, "password": {"type": "string", "default": "d6e1f181c6c2794be8f0"}, "rootPassword": {"$ref": "#/properties/json/properties/schema/properties/services/items/anyOf/1/properties/data/properties/password"}, "resources": {"$ref": "#/properties/json/properties/schema/properties/services/items/anyOf/0/properties/data/properties/resources"}, "backup": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "schedule": {"type": "string", "minLength": 1}, "destinationId": {"type": "string", "minLength": 1}, "prefix": {"type": "string", "pattern": "^[\\w-/.]+$"}, "databaseName": {"type": "string"}}, "required": ["enabled", "schedule", "destinationId", "prefix"], "additionalProperties": false}, "env": {"type": "string"}, "configFile": {"type": "string"}, "command": {"type": "string"}, "phpMyAdmin": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}, "token": {"type": "string", "default": "d90c6f14cee86751df5e"}}, "additionalProperties": false}, "dbGate": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}, "token": {"type": "string", "default": "0180b5ba118464582073"}}, "additionalProperties": false}}, "required": ["serviceName"], "additionalProperties": false}}, "required": ["type", "data"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "ma<PERSON>b"}, "data": {"type": "object", "properties": {"serviceName": {"$ref": "#/properties/json/properties/schema/properties/services/items/anyOf/0/properties/data/properties/serviceName"}, "databaseName": {"$ref": "#/properties/json/properties/schema/properties/services/items/anyOf/1/properties/data/properties/databaseName"}, "user": {"$ref": "#/properties/json/properties/schema/properties/services/items/anyOf/1/properties/data/properties/user"}, "image": {"type": "string", "default": "mariadb:11"}, "exposedPort": {"type": "number"}, "password": {"$ref": "#/properties/json/properties/schema/properties/services/items/anyOf/1/properties/data/properties/password"}, "rootPassword": {"$ref": "#/properties/json/properties/schema/properties/services/items/anyOf/1/properties/data/properties/password"}, "resources": {"$ref": "#/properties/json/properties/schema/properties/services/items/anyOf/0/properties/data/properties/resources"}, "backup": {"$ref": "#/properties/json/properties/schema/properties/services/items/anyOf/1/properties/data/properties/backup"}, "env": {"type": "string"}, "configFile": {"type": "string"}, "command": {"type": "string"}, "phpMyAdmin": {"$ref": "#/properties/json/properties/schema/properties/services/items/anyOf/1/properties/data/properties/phpMyAdmin"}, "dbGate": {"$ref": "#/properties/json/properties/schema/properties/services/items/anyOf/1/properties/data/properties/dbGate"}}, "required": ["serviceName"], "additionalProperties": false}}, "required": ["type", "data"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "mongo"}, "data": {"type": "object", "properties": {"serviceName": {"$ref": "#/properties/json/properties/schema/properties/services/items/anyOf/0/properties/data/properties/serviceName"}, "user": {"$ref": "#/properties/json/properties/schema/properties/services/items/anyOf/1/properties/data/properties/user"}, "image": {"type": "string", "default": "mongo:8"}, "exposedPort": {"type": "number"}, "password": {"$ref": "#/properties/json/properties/schema/properties/services/items/anyOf/1/properties/data/properties/password"}, "backup": {"$ref": "#/properties/json/properties/schema/properties/services/items/anyOf/1/properties/data/properties/backup"}, "resources": {"$ref": "#/properties/json/properties/schema/properties/services/items/anyOf/0/properties/data/properties/resources"}, "env": {"type": "string"}, "command": {"type": "string"}, "mongoExpress": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}, "token": {"type": "string", "default": "4fdaee3b9a129c5256d0"}}, "additionalProperties": false}, "dbGate": {"$ref": "#/properties/json/properties/schema/properties/services/items/anyOf/1/properties/data/properties/dbGate"}}, "required": ["serviceName"], "additionalProperties": false}}, "required": ["type", "data"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "postgres"}, "data": {"type": "object", "properties": {"serviceName": {"$ref": "#/properties/json/properties/schema/properties/services/items/anyOf/0/properties/data/properties/serviceName"}, "databaseName": {"$ref": "#/properties/json/properties/schema/properties/services/items/anyOf/1/properties/data/properties/databaseName"}, "user": {"$ref": "#/properties/json/properties/schema/properties/services/items/anyOf/1/properties/data/properties/user"}, "image": {"type": "string", "default": "postgres:17"}, "exposedPort": {"type": "number"}, "password": {"$ref": "#/properties/json/properties/schema/properties/services/items/anyOf/1/properties/data/properties/password"}, "backup": {"$ref": "#/properties/json/properties/schema/properties/services/items/anyOf/1/properties/data/properties/backup"}, "resources": {"$ref": "#/properties/json/properties/schema/properties/services/items/anyOf/0/properties/data/properties/resources"}, "env": {"type": "string"}, "command": {"type": "string"}, "pgWeb": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}, "token": {"type": "string", "default": "1b834fb4e8ca340d90a1"}}, "additionalProperties": false}, "dbGate": {"$ref": "#/properties/json/properties/schema/properties/services/items/anyOf/1/properties/data/properties/dbGate"}}, "required": ["serviceName"], "additionalProperties": false}}, "required": ["type", "data"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "redis"}, "data": {"type": "object", "properties": {"serviceName": {"$ref": "#/properties/json/properties/schema/properties/services/items/anyOf/0/properties/data/properties/serviceName"}, "image": {"type": "string", "default": "redis:7"}, "exposedPort": {"type": "number"}, "password": {"$ref": "#/properties/json/properties/schema/properties/services/items/anyOf/1/properties/data/properties/password"}, "resources": {"$ref": "#/properties/json/properties/schema/properties/services/items/anyOf/0/properties/data/properties/resources"}, "env": {"type": "string"}, "command": {"type": "string"}, "redisCommander": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}, "token": {"type": "string", "default": "886841fc167eda397a80"}}, "additionalProperties": false}, "dbGate": {"$ref": "#/properties/json/properties/schema/properties/services/items/anyOf/1/properties/data/properties/dbGate"}}, "required": ["serviceName"], "additionalProperties": false}}, "required": ["type", "data"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "compose"}, "data": {"type": "object", "properties": {"serviceName": {"$ref": "#/properties/json/properties/schema/properties/services/items/anyOf/0/properties/data/properties/serviceName"}, "source": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "inline"}, "content": {"type": "string"}}, "required": ["type", "content"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "git"}, "repo": {"type": "string"}, "ref": {"type": "string"}, "rootPath": {"type": "string"}, "composeFile": {"type": "string"}}, "required": ["type", "repo", "ref", "rootPath", "composeFile"], "additionalProperties": false}]}, "env": {"type": "string", "default": ""}, "createDotEnv": {"type": "boolean", "default": false}, "domains": {"$ref": "#/properties/json/properties/schema/properties/services/items/anyOf/0/properties/data/properties/domains"}, "redirects": {"type": "array", "items": {"type": "object", "properties": {"regex": {"type": "string"}, "replacement": {"type": "string"}, "permanent": {"type": "boolean"}, "enabled": {"type": "boolean"}}, "required": ["regex", "replacement", "permanent", "enabled"], "additionalProperties": false}}, "basicAuth": {"type": "array", "items": {"type": "object", "properties": {"username": {"type": "string"}, "password": {"type": "string"}}, "required": ["username", "password"], "additionalProperties": false}}, "maintenance": {"$ref": "#/properties/json/properties/schema/properties/services/items/anyOf/0/properties/data/properties/maintenance"}}, "required": ["serviceName"], "additionalProperties": false}}, "required": ["type", "data"], "additionalProperties": false}]}}}, "required": ["services"], "additionalProperties": false}}, "required": ["projectName", "schema"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/users.generateApiToken": {"post": {"operationId": "users.generateApiToken", "security": [{"bearerAuth": []}], "tags": ["Users"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/users.revokeApiToken": {"post": {"operationId": "users.revokeApiToken", "security": [{"bearerAuth": []}], "tags": ["Users"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/users.createUser": {"post": {"operationId": "users.createUser", "security": [{"bearerAuth": []}], "tags": ["Users"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "password": {"type": "string"}, "admin": {"type": "boolean"}}, "required": ["email", "password", "admin"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/users.updateUser": {"post": {"operationId": "users.updateUser", "security": [{"bearerAuth": []}], "tags": ["Users"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"id": {"type": "string"}, "password": {"type": "string"}, "admin": {"type": "boolean"}}, "required": ["id", "admin"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/users.destroyUser": {"post": {"operationId": "users.destroyUser", "security": [{"bearerAuth": []}], "tags": ["Users"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/twoFactor.configure": {"post": {"operationId": "twoFactor.configure", "security": [{"bearerAuth": []}], "tags": ["Two Factor"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {}}}}}, "/api/trpc/twoFactor.enable": {"post": {"operationId": "twoFactor.enable", "security": [{"bearerAuth": []}], "tags": ["Two Factor"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"code": {"type": "string"}}, "required": ["code"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/twoFactor.disable": {"post": {"operationId": "twoFactor.disable", "security": [{"bearerAuth": []}], "tags": ["Two Factor"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {}}}}}, "/api/trpc/git.generateKey": {"post": {"operationId": "git.<PERSON><PERSON>ey", "security": [{"bearerAuth": []}], "tags": ["Git"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["serviceName", "projectName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/cluster.removeNode": {"post": {"operationId": "cluster.removeNode", "security": [{"bearerAuth": []}], "tags": ["Cluster"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/branding.setErrorPageSettings": {"post": {"operationId": "branding.setErrorPageSettings", "security": [{"bearerAuth": []}], "tags": ["Branding"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"customCss": {"type": "string"}, "hideLogo": {"type": "boolean"}, "hideLinks": {"type": "boolean"}}, "required": ["<PERSON><PERSON><PERSON>", "hideLinks"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/branding.setBasicSettings": {"post": {"operationId": "branding.setBasicSettings", "security": [{"bearerAuth": []}], "tags": ["Branding"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"hideIp": {"type": "boolean"}, "hideNotes": {"type": "boolean"}, "serverName": {"type": "string"}, "serverColor": {"type": "string"}}, "required": ["hideIp", "hideNotes", "serverName", "serverColor"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/branding.setLogoSettings": {"post": {"operationId": "branding.setLogoSettings", "security": [{"bearerAuth": []}], "tags": ["Branding"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"lightLogoMark": {"type": "string"}, "darkLogoMark": {"type": "string"}, "lightLogo": {"type": "string"}, "darkLogo": {"type": "string"}}, "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/branding.setCustomCodeSettings": {"post": {"operationId": "branding.setCustomCodeSettings", "security": [{"bearerAuth": []}], "tags": ["Branding"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"customCode": {"type": "string"}}, "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/branding.setLinksSettings": {"post": {"operationId": "branding.setLinksSettings", "security": [{"bearerAuth": []}], "tags": ["Branding"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"hideDocumentationLink": {"type": "boolean"}, "hideDiscordLink": {"type": "boolean"}, "hideFeedbackLink": {"type": "boolean"}, "hideChangelogLink": {"type": "boolean"}, "hideOtherLinks": {"type": "boolean"}}, "required": ["hideDocumentationLink", "hideDiscordLink", "hideFeedbackLink", "hideChangelogLink", "hideOtherLinks"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/notifications.sendTestNotification": {"post": {"operationId": "notifications.sendTestNotification", "security": [{"bearerAuth": []}], "tags": ["Notifications"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"name": {"type": "string"}, "target": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "discord"}, "url": {"type": "string", "format": "uri"}}, "required": ["type", "url"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "telegram"}, "access_token": {"type": "string"}, "chat_id": {"type": "string"}}, "required": ["type", "access_token", "chat_id"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "slack"}, "url": {"type": "string", "format": "uri"}}, "required": ["type", "url"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "smtp"}, "host": {"type": "string"}, "port": {"type": "number"}, "username": {"type": "string"}, "password": {"type": "string"}, "recipients": {"type": "array", "items": {"type": "string", "format": "email"}}}, "required": ["type", "host", "port", "username", "password", "recipients"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "webhook"}, "url": {"type": "string"}, "secret": {"type": "string"}}, "required": ["type", "url"], "additionalProperties": false}]}, "events": {"type": "object", "properties": {"updateAvailable": {"type": "object", "properties": {"enabled": {"type": "boolean"}}, "required": ["enabled"], "additionalProperties": false, "default": {"enabled": false}}, "dockerCleanup": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}}, "additionalProperties": false, "default": {"enabled": false}}, "diskLoad": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}, "min": {"type": "number"}, "schedule": {"type": "string"}}, "required": ["min", "schedule"], "additionalProperties": false, "default": {"enabled": false, "min": 80, "schedule": "0 2 * * *"}}, "appDeploy": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}}, "additionalProperties": false, "default": {"enabled": false}}, "databaseBackup": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}}, "additionalProperties": false, "default": {"enabled": false}}}, "additionalProperties": false}}, "required": ["name", "target", "events"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/notifications.createNotificationChannel": {"post": {"operationId": "notifications.createNotificationChannel", "security": [{"bearerAuth": []}], "tags": ["Notifications"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"name": {"type": "string"}, "target": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "discord"}, "url": {"type": "string", "format": "uri"}}, "required": ["type", "url"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "telegram"}, "access_token": {"type": "string"}, "chat_id": {"type": "string"}}, "required": ["type", "access_token", "chat_id"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "slack"}, "url": {"type": "string", "format": "uri"}}, "required": ["type", "url"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "smtp"}, "host": {"type": "string"}, "port": {"type": "number"}, "username": {"type": "string"}, "password": {"type": "string"}, "recipients": {"type": "array", "items": {"type": "string", "format": "email"}}}, "required": ["type", "host", "port", "username", "password", "recipients"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "webhook"}, "url": {"type": "string"}, "secret": {"type": "string"}}, "required": ["type", "url"], "additionalProperties": false}]}, "events": {"type": "object", "properties": {"updateAvailable": {"type": "object", "properties": {"enabled": {"type": "boolean"}}, "required": ["enabled"], "additionalProperties": false, "default": {"enabled": false}}, "dockerCleanup": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}}, "additionalProperties": false, "default": {"enabled": false}}, "diskLoad": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}, "min": {"type": "number"}, "schedule": {"type": "string"}}, "required": ["min", "schedule"], "additionalProperties": false, "default": {"enabled": false, "min": 80, "schedule": "0 2 * * *"}}, "appDeploy": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}}, "additionalProperties": false, "default": {"enabled": false}}, "databaseBackup": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}}, "additionalProperties": false, "default": {"enabled": false}}}, "additionalProperties": false}}, "required": ["name", "target", "events"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/notifications.updateNotificationChannel": {"post": {"operationId": "notifications.updateNotificationChannel", "security": [{"bearerAuth": []}], "tags": ["Notifications"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "target": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "discord"}, "url": {"type": "string", "format": "uri"}}, "required": ["type", "url"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "telegram"}, "access_token": {"type": "string"}, "chat_id": {"type": "string"}}, "required": ["type", "access_token", "chat_id"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "slack"}, "url": {"type": "string", "format": "uri"}}, "required": ["type", "url"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "smtp"}, "host": {"type": "string"}, "port": {"type": "number"}, "username": {"type": "string"}, "password": {"type": "string"}, "recipients": {"type": "array", "items": {"type": "string", "format": "email"}}}, "required": ["type", "host", "port", "username", "password", "recipients"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "webhook"}, "url": {"type": "string"}, "secret": {"type": "string"}}, "required": ["type", "url"], "additionalProperties": false}]}, "events": {"type": "object", "properties": {"updateAvailable": {"type": "object", "properties": {"enabled": {"type": "boolean"}}, "required": ["enabled"], "additionalProperties": false, "default": {"enabled": false}}, "dockerCleanup": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}}, "additionalProperties": false, "default": {"enabled": false}}, "diskLoad": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}, "min": {"type": "number"}, "schedule": {"type": "string"}}, "required": ["min", "schedule"], "additionalProperties": false, "default": {"enabled": false, "min": 80, "schedule": "0 2 * * *"}}, "appDeploy": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}}, "additionalProperties": false, "default": {"enabled": false}}, "databaseBackup": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}}, "additionalProperties": false, "default": {"enabled": false}}}, "additionalProperties": false}}, "required": ["id", "name", "target", "events"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/notifications.destroyNotificationChannel": {"post": {"operationId": "notifications.destroyNotificationChannel", "security": [{"bearerAuth": []}], "tags": ["Notifications"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/certificates.removeCertificate": {"post": {"operationId": "certificates.removeCertificate", "security": [{"bearerAuth": []}], "tags": ["Certificates"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"domain": {"type": "string"}}, "required": ["domain"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/actions.killAction": {"post": {"operationId": "actions.killAction", "security": [{"bearerAuth": []}], "tags": ["Actions"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/traefik.restart": {"post": {"operationId": "traefik.restart", "security": [{"bearerAuth": []}], "tags": ["Traefik"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {}}}}}, "/api/trpc/traefik.setEnv": {"post": {"operationId": "traefik.setEnv", "security": [{"bearerAuth": []}], "tags": ["Traefik"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"env": {"type": "string", "default": ""}}, "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/traefik.setCustomConfig": {"post": {"operationId": "traefik.setCustomConfig", "security": [{"bearerAuth": []}], "tags": ["Traefik"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"config": {"type": "string", "default": ""}}, "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/server.reboot": {"post": {"operationId": "server.reboot", "security": [{"bearerAuth": []}], "tags": ["Server"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {}}}}}, "/api/trpc/cloudflareTunnel.setConfig": {"post": {"operationId": "cloudflareTunnel.setConfig", "security": [{"bearerAuth": []}], "tags": ["Cloudflare Tunnel"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"apiToken": {"type": "string"}, "accountId": {"type": "string"}, "tunnelId": {"type": "string"}}, "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/cloudflareTunnel.createTunnelRule": {"post": {"operationId": "cloudflareTunnel.createTunnelRule", "security": [{"bearerAuth": []}], "tags": ["Cloudflare Tunnel"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "subdomain": {"type": "string"}, "domain": {"type": "string"}, "path": {"type": "string"}, "internalProtocol": {"type": "string", "enum": ["http", "https"]}, "internalPort": {"type": "number"}, "zoneId": {"type": "string"}}, "required": ["projectName", "serviceName", "subdomain", "domain", "path", "internalProtocol", "internalPort", "zoneId"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/cloudflareTunnel.updateTunnelRule": {"post": {"operationId": "cloudflareTunnel.updateTunnelRule", "security": [{"bearerAuth": []}], "tags": ["Cloudflare Tunnel"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"id": {"type": "string"}, "projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "subdomain": {"type": "string"}, "domain": {"type": "string"}, "path": {"type": "string"}, "internalProtocol": {"type": "string", "enum": ["http", "https"]}, "internalPort": {"type": "number"}, "zoneId": {"type": "string"}, "dnsRecordId": {"type": "string"}}, "required": ["id", "projectName", "serviceName", "subdomain", "domain", "path", "internalProtocol", "internalPort", "zoneId", "dnsRecordId"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/cloudflareTunnel.deleteTunnelRule": {"post": {"operationId": "cloudflareTunnel.deleteTunnelRule", "security": [{"bearerAuth": []}], "tags": ["Cloudflare Tunnel"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/cloudflareTunnel.startTunnel": {"post": {"operationId": "cloudflareTunnel.startTunnel", "security": [{"bearerAuth": []}], "tags": ["Cloudflare Tunnel"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {}}}}}, "/api/trpc/cloudflareTunnel.stopTunnel": {"post": {"operationId": "cloudflareTunnel.stopTunnel", "security": [{"bearerAuth": []}], "tags": ["Cloudflare Tunnel"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {}}}}}, "/api/trpc/domains.createDomain": {"post": {"operationId": "domains.createDomain", "security": [{"bearerAuth": []}], "tags": ["Domains"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {}, "additionalProperties": false, "description": "Unknown object properties"}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/domains.updateDomain": {"post": {"operationId": "domains.updateDomain", "security": [{"bearerAuth": []}], "tags": ["Domains"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {}, "additionalProperties": false, "description": "Unknown object properties"}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/domains.deleteDomain": {"post": {"operationId": "domains.deleteDomain", "security": [{"bearerAuth": []}], "tags": ["Domains"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/domains.setPrimaryDomain": {"post": {"operationId": "domains.setPrimaryDomain", "security": [{"bearerAuth": []}], "tags": ["Domains"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/storageProviders.dropbox.createProvider": {"post": {"operationId": "storageProviders.dropbox.createProvider", "security": [{"bearerAuth": []}], "tags": ["Storage Providers / Dropbox"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"name": {"type": "string"}}, "required": ["name"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/storageProviders.dropbox.updateProvider": {"post": {"operationId": "storageProviders.dropbox.updateProvider", "security": [{"bearerAuth": []}], "tags": ["Storage Providers / Dropbox"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}, "required": ["id"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/storageProviders.dropbox.deleteProvider": {"post": {"operationId": "storageProviders.dropbox.deleteProvider", "security": [{"bearerAuth": []}], "tags": ["Storage Providers / Dropbox"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/storageProviders.dropbox.disconnectProvider": {"post": {"operationId": "storageProviders.dropbox.disconnectProvider", "security": [{"bearerAuth": []}], "tags": ["Storage Providers / Dropbox"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/storageProviders.google.createProvider": {"post": {"operationId": "storageProviders.google.createProvider", "security": [{"bearerAuth": []}], "tags": ["Storage Providers / Google"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"name": {"type": "string"}}, "required": ["name"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/storageProviders.google.updateProvider": {"post": {"operationId": "storageProviders.google.updateProvider", "security": [{"bearerAuth": []}], "tags": ["Storage Providers / Google"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}, "required": ["id"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/storageProviders.google.deleteProvider": {"post": {"operationId": "storageProviders.google.deleteProvider", "security": [{"bearerAuth": []}], "tags": ["Storage Providers / Google"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/storageProviders.google.disconnectProvider": {"post": {"operationId": "storageProviders.google.disconnectProvider", "security": [{"bearerAuth": []}], "tags": ["Storage Providers / Google"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/storageProviders.s3.createProvider": {"post": {"operationId": "storageProviders.s3.createProvider", "security": [{"bearerAuth": []}], "tags": ["Storage Providers / S 3"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"subtype": {"type": "string", "enum": ["other", "aws", "backblaze", "digital-ocean", "wasabi"]}, "name": {"type": "string"}, "accessKeyId": {"type": "string"}, "secretAccessKey": {"type": "string"}, "bucket": {"type": "string"}, "region": {"type": "string"}, "endpoint": {"type": "string"}}, "required": ["subtype", "name", "accessKeyId", "secretAccessKey", "bucket", "region"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/storageProviders.s3.updateProvider": {"post": {"operationId": "storageProviders.s3.updateProvider", "security": [{"bearerAuth": []}], "tags": ["Storage Providers / S 3"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "accessKeyId": {"type": "string"}, "secretAccessKey": {"type": "string"}, "bucket": {"type": "string"}, "region": {"type": "string"}, "endpoint": {"type": "string"}}, "required": ["id", "name", "accessKeyId", "secretAccessKey", "bucket", "region"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/storageProviders.s3.deleteProvider": {"post": {"operationId": "storageProviders.s3.deleteProvider", "security": [{"bearerAuth": []}], "tags": ["Storage Providers / S 3"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/storageProviders.sftp.createProvider": {"post": {"operationId": "storageProviders.sftp.createProvider", "security": [{"bearerAuth": []}], "tags": ["Storage Providers / Sftp"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"name": {"type": "string"}, "host": {"type": "string"}, "username": {"type": "string"}, "password": {"type": "string"}, "port": {"type": "string"}}, "required": ["name", "host", "username", "password"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/storageProviders.sftp.updateProvider": {"post": {"operationId": "storageProviders.sftp.updateProvider", "security": [{"bearerAuth": []}], "tags": ["Storage Providers / Sftp"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "host": {"type": "string"}, "username": {"type": "string"}, "password": {"type": "string"}, "port": {"type": "string"}}, "required": ["id", "name", "host", "username", "password"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/storageProviders.sftp.deleteProvider": {"post": {"operationId": "storageProviders.sftp.deleteProvider", "security": [{"bearerAuth": []}], "tags": ["Storage Providers / Sftp"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/storageProviders.local.createProvider": {"post": {"operationId": "storageProviders.local.createProvider", "security": [{"bearerAuth": []}], "tags": ["Storage Providers / Local"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"name": {"type": "string"}, "path": {"type": "string"}}, "required": ["name", "path"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/storageProviders.local.updateProvider": {"post": {"operationId": "storageProviders.local.updateProvider", "security": [{"bearerAuth": []}], "tags": ["Storage Providers / Local"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "path": {"type": "string"}}, "required": ["id", "name", "path"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/storageProviders.local.deleteProvider": {"post": {"operationId": "storageProviders.local.deleteProvider", "security": [{"bearerAuth": []}], "tags": ["Storage Providers / Local"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/storageProviders.ftp.createProvider": {"post": {"operationId": "storageProviders.ftp.createProvider", "security": [{"bearerAuth": []}], "tags": ["Storage Providers / Ftp"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"name": {"type": "string"}, "host": {"type": "string"}, "username": {"type": "string"}, "password": {"type": "string"}, "port": {"type": "string"}}, "required": ["name", "host", "username", "password", "port"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/storageProviders.ftp.updateProvider": {"post": {"operationId": "storageProviders.ftp.updateProvider", "security": [{"bearerAuth": []}], "tags": ["Storage Providers / Ftp"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "host": {"type": "string"}, "username": {"type": "string"}, "password": {"type": "string"}, "port": {"type": "string"}}, "required": ["id", "name", "host", "username", "password"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/storageProviders.ftp.deleteProvider": {"post": {"operationId": "storageProviders.ftp.deleteProvider", "security": [{"bearerAuth": []}], "tags": ["Storage Providers / Ftp"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/backups.clearBackupLog": {"post": {"operationId": "backups.clearBackupLog", "security": [{"bearerAuth": []}], "tags": ["Backups"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/dockerBuilders.createDockerBuilder": {"post": {"operationId": "dockerBuilders.createDockerBuilder", "security": [{"bearerAuth": []}], "tags": ["Docker Builders"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"name": {"type": "string"}, "memory": {"type": "number", "default": 2048}, "memorySwap": {"type": "number", "default": 4096}, "cpus": {"type": "number", "default": 2}}, "required": ["name"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/dockerBuilders.useDockerBuilder": {"post": {"operationId": "dockerBuilders.useDockerBuilder", "security": [{"bearerAuth": []}], "tags": ["Docker Builders"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"name": {"type": "string"}}, "required": ["name"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/dockerBuilders.stopDockerBuilder": {"post": {"operationId": "dockerBuilders.stopDockerBuilder", "security": [{"bearerAuth": []}], "tags": ["Docker Builders"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"name": {"type": "string"}}, "required": ["name"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/dockerBuilders.removeDockerBuilder": {"post": {"operationId": "dockerBuilders.removeDockerBuilder", "security": [{"bearerAuth": []}], "tags": ["Docker Builders"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"name": {"type": "string"}}, "required": ["name"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/middlewares.createMiddleware": {"post": {"operationId": "middlewares.createMiddleware", "security": [{"bearerAuth": []}], "tags": ["Middlewares"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {}, "additionalProperties": false, "description": "Unknown object properties"}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/middlewares.updateMiddleware": {"post": {"operationId": "middlewares.updateMiddleware", "security": [{"bearerAuth": []}], "tags": ["Middlewares"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {}, "additionalProperties": false, "description": "Unknown object properties"}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/middlewares.destroyMiddleware": {"post": {"operationId": "middlewares.destroyMiddleware", "security": [{"bearerAuth": []}], "tags": ["Middlewares"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/volumeBackups.createVolumeBackup": {"post": {"operationId": "volumeBackups.createVolumeBackup", "security": [{"bearerAuth": []}], "tags": ["Volume Backups"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"storageProviderId": {"type": "string", "minLength": 1}, "storageProviderPath": {"type": "string", "pattern": "^[\\w-/.]+$"}, "enabled": {"type": "boolean"}, "schedule": {"type": "string", "minLength": 1}, "volumeName": {"type": "string"}, "projectName": {"type": "string"}, "serviceName": {"type": "string"}}, "required": ["storageProviderId", "storageProviderPath", "enabled", "schedule", "volumeName", "projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/volumeBackups.updateVolumeBackup": {"post": {"operationId": "volumeBackups.updateVolumeBackup", "security": [{"bearerAuth": []}], "tags": ["Volume Backups"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"storageProviderId": {"type": "string", "minLength": 1}, "storageProviderPath": {"type": "string", "pattern": "^[\\w-/.]+$"}, "enabled": {"type": "boolean"}, "schedule": {"type": "string", "minLength": 1}, "volumeName": {"type": "string"}, "projectName": {"type": "string"}, "serviceName": {"type": "string"}, "id": {"type": "string"}}, "required": ["storageProviderId", "storageProviderPath", "enabled", "schedule", "volumeName", "projectName", "serviceName", "id"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/volumeBackups.destroyVolumeBackup": {"post": {"operationId": "volumeBackups.destroyVolumeBackup", "security": [{"bearerAuth": []}], "tags": ["Volume Backups"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/volumeBackups.runVolumeBackup": {"post": {"operationId": "volumeBackups.runVolumeBackup", "security": [{"bearerAuth": []}], "tags": ["Volume Backups"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/mounts.createMount": {"post": {"operationId": "mounts.createMount", "security": [{"bearerAuth": []}], "tags": ["Mounts"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "values": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "bind"}, "hostPath": {"type": "string", "minLength": 1}, "mountPath": {"type": "string", "minLength": 1}}, "required": ["type", "hostPath", "mount<PERSON>ath"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "volume"}, "name": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "mountPath": {"type": "string", "minLength": 1}}, "required": ["type", "name", "mount<PERSON>ath"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "file"}, "content": {"type": "string"}, "mountPath": {"type": "string", "minLength": 1}}, "required": ["type", "content", "mount<PERSON>ath"], "additionalProperties": false}]}}, "required": ["projectName", "serviceName", "values"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/mounts.updateMount": {"post": {"operationId": "mounts.updateMount", "security": [{"bearerAuth": []}], "tags": ["Mounts"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "index": {"type": "number"}, "values": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "bind"}, "hostPath": {"type": "string", "minLength": 1}, "mountPath": {"type": "string", "minLength": 1}}, "required": ["type", "hostPath", "mount<PERSON>ath"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "volume"}, "name": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "mountPath": {"type": "string", "minLength": 1}}, "required": ["type", "name", "mount<PERSON>ath"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "file"}, "content": {"type": "string"}, "mountPath": {"type": "string", "minLength": 1}}, "required": ["type", "content", "mount<PERSON>ath"], "additionalProperties": false}]}}, "required": ["projectName", "serviceName", "index", "values"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/mounts.deleteMount": {"post": {"operationId": "mounts.deleteMount", "security": [{"bearerAuth": []}], "tags": ["Mounts"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "index": {"type": "number"}}, "required": ["projectName", "serviceName", "index"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/ports.createPort": {"post": {"operationId": "ports.createPort", "security": [{"bearerAuth": []}], "tags": ["Ports"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "values": {"type": "object", "properties": {"published": {"type": "number"}, "target": {"type": "number"}, "protocol": {"type": "string", "enum": ["tcp", "udp"], "default": "tcp"}}, "required": ["published", "target"], "additionalProperties": false}}, "required": ["projectName", "serviceName", "values"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/ports.updatePort": {"post": {"operationId": "ports.updatePort", "security": [{"bearerAuth": []}], "tags": ["Ports"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "index": {"type": "number"}, "values": {"type": "object", "properties": {"published": {"type": "number"}, "target": {"type": "number"}, "protocol": {"type": "string", "enum": ["tcp", "udp"], "default": "tcp"}}, "required": ["published", "target"], "additionalProperties": false}}, "required": ["projectName", "serviceName", "index", "values"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/ports.deletePort": {"post": {"operationId": "ports.deletePort", "security": [{"bearerAuth": []}], "tags": ["Ports"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "index": {"type": "number"}}, "required": ["projectName", "serviceName", "index"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/ports.deleteAllPorts": {"post": {"operationId": "ports.deleteAllPorts", "security": [{"bearerAuth": []}], "tags": ["Ports"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}}, "required": ["projectName", "serviceName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/databaseBackups.createDatabaseBackup": {"post": {"operationId": "databaseBackups.createDatabaseBackup", "security": [{"bearerAuth": []}], "tags": ["Database Backups"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "enabled": {"type": "boolean"}, "schedule": {"type": "string"}, "databaseName": {"type": "string", "pattern": "^[a-zA-Z][a-zA-Z0-9_]{0,62}$"}, "storageProviderId": {"type": "string"}, "storageProviderPath": {"type": "string"}}, "required": ["projectName", "serviceName", "enabled", "schedule", "databaseName", "storageProviderId", "storageProviderPath"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/databaseBackups.updateDatabaseBackup": {"post": {"operationId": "databaseBackups.updateDatabaseBackup", "security": [{"bearerAuth": []}], "tags": ["Database Backups"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"id": {"type": "string"}, "projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "enabled": {"type": "boolean"}, "schedule": {"type": "string"}, "databaseName": {"type": "string", "pattern": "^[a-zA-Z][a-zA-Z0-9_]{0,62}$"}, "storageProviderId": {"type": "string"}, "storageProviderPath": {"type": "string"}}, "required": ["id", "projectName", "serviceName", "enabled", "schedule", "databaseName", "storageProviderId", "storageProviderPath"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/databaseBackups.deleteDatabaseBackup": {"post": {"operationId": "databaseBackups.deleteDatabaseBackup", "security": [{"bearerAuth": []}], "tags": ["Database Backups"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/databaseBackups.runDatabaseBackup": {"post": {"operationId": "databaseBackups.runDatabaseBackup", "security": [{"bearerAuth": []}], "tags": ["Database Backups"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/databaseBackups.restoreDatabaseBackup": {"post": {"operationId": "databaseBackups.restoreDatabaseBackup", "security": [{"bearerAuth": []}], "tags": ["Database Backups"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"json": {"type": "object", "properties": {"projectName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "serviceName": {"type": "string", "pattern": "^[a-z0-9-_]+$"}, "storageProviderId": {"type": "string"}, "path": {"type": "string"}, "databaseName": {"type": "string"}}, "required": ["projectName", "serviceName", "storageProviderId", "path", "databaseName"], "additionalProperties": false}}, "required": ["json"], "additionalProperties": false}}}}}}, "/api/trpc/subscription.onInvalidateActions": {"post": {"operationId": "subscription.onInvalidateActions", "security": [{"bearerAuth": []}], "tags": ["Subscription"], "responses": {"200": {"description": ""}}, "requestBody": {"content": {"application/json": {}}}}}}}